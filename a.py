import requests
import json
from datetime import datetime
import time
import traceback
import pytz
import os

# 配置
BASE_API_URL = "http://172.25.165.28:8055"
API_TOKEN = "OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM"
TEMU_BASE_URL = "https://www.temu.com"
TEMU_API_PATH = "/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
SHOP_DELAY = 60  # 店铺间隔60秒

# 辅助函数
def convert_to_int(value):
    """将各种格式的值转换为整数"""
    if value is None:
        return 0

    if isinstance(value, int):
        return value

    if isinstance(value, float):
        return int(value)

    if isinstance(value, str):
        return extract_number_from_string(value)

    return 0

def extract_number_from_string(text):
    """从字符串中提取数字，处理带有'万'等单位的情况"""
    if not text or not isinstance(text, str):
        return 0

    try:
        # 处理带有"万"的情况
        if "万" in text:
            import re
            numbers = re.findall(r'[\d.]+', text)
            if numbers:
                return int(float(numbers[0]) * 10000)

        # 处理普通数字
        import re
        numbers = re.findall(r'\d+', text)
        if numbers:
            return int(numbers[0])

        return 0
    except Exception as e:
        print(f"从字符串'{text}'提取数字时出错: {str(e)}")
        return 0

def get_hk_time():
    """获取香港时区的当前时间"""
    hk_timezone = pytz.timezone('Asia/Hong_Kong')
    return datetime.now(hk_timezone).strftime('%Y-%m-%d %H:%M:%S')

def get_headers():
    """返回带有认证令牌的API请求头"""
    return {
        "Authorization": "Bearer " + API_TOKEN,
        "Content-Type": "application/json"
    }

def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        print(f"尝试从 {cookie_file_path} 加载cookie")

        if not os.path.exists(cookie_file_path):
            print(f"警告: cookie文件不存在: {cookie_file_path}")
            return None

        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)

        # 将cookie列表转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])

        print(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        return cookie_str
    except Exception as e:
        print(f"加载cookie文件时出错: {str(e)}")
        return None

def get_temu_headers():
    """返回Temu API请求的请求头"""
    fresh_cookies = load_cookies_from_file()
    if not fresh_cookies:
        print("警告: 无法获取cookie，请检查cookie.json文件")
        return None

    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
        "content-type": "application/json;charset=UTF-8",
        "origin": "https://www.temu.com",
        "priority": "u=1, i",
        "referer": "https://www.temu.com/",
        "sec-ch-ua": '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36",
        "Cookie": fresh_cookies
    }

    return headers

def fetch_temu_shop_data(mall_id):
    """从Temu API获取商店和产品数据"""
    url = TEMU_BASE_URL + TEMU_API_PATH
    payload = {
        "mallId": mall_id,
        "mainGoodsIds": ["1"],
        "source_page_sn": "10013",
        "mall_id": mall_id,
        "main_goods_ids": ["1"],
        "filter_items": "",
        "page_number": 1,
        "page_size": 8,
        "list_id": "r7oe7gyw0vd5xo2z2qja2",
        "scene_code": "mall_rule",
        "page_sn": 10040,
        "page_el_sn": 201265,
        "source": 10018,
        "anti_content": "1"
    }

    print(f"请求Temu数据: {url}, mall_id: {mall_id}")

    headers = get_temu_headers()
    if not headers:
        print("无法获取有效的请求头，跳过请求")
        return None

    try:
        response = requests.post(url, headers=headers, json=payload)
        print(f"Temu API状态码: {response.status_code}")

        if response.status_code != 200:
            print(f"获取mall_id为{mall_id}的Temu数据时出错: {response.status_code}")
            print(f"返回内容: {response.text[:500]}...")
            return None

        result = response.json()
        return result
    except Exception as e:
        print(f"获取Temu数据时发生异常: {str(e)}")
        traceback.print_exc()
        return None

def save_product_data(product_data):
    """将产品数据保存到数据库，如果已存在则更新"""
    if not product_data:
        print("警告: 产品数据为空，跳过保存")
        return False

    goods_id = product_data.get('goods_id')
    mall_id = product_data.get('mall_id')

    if not goods_id:
        print("错误: 产品数据缺少goods_id，跳过保存")
        return False

    print(f"保存/更新产品数据: goods_id={goods_id}")

    try:
        # 首先获取shop_data的id
        shop_check_url = f"{BASE_API_URL}/items/shop_data?filter[mall_id][_eq]={mall_id}"
        shop_response = requests.get(shop_check_url, headers=get_headers())

        if shop_response.status_code != 200:
            print(f"获取shop_data id时出错: {shop_response.status_code}")
            return False

        shop_data_result = shop_response.json()
        if not shop_data_result.get('data') or len(shop_data_result['data']) == 0:
            print(f"未找到对应的shop_data记录: {mall_id}")
            return False

        shop_data_id = shop_data_result['data'][0]['id']

        # 添加shop_data外键
        product_data['shop_data'] = shop_data_id

        # 删除mall_id字段，因为数据库中没有这个字段
        if 'mall_id' in product_data:
            del product_data['mall_id']

        # 检查该goods_id是否已存在
        check_url = f"{BASE_API_URL}/items/product_data?filter[goods_id][_eq]={goods_id}"
        check_response = requests.get(check_url, headers=get_headers())

        if check_response.status_code != 200:
            print(f"检查产品数据时出错: {check_response.status_code}")
            return False

        existing_data = check_response.json()

        if existing_data and existing_data.get('data') and len(existing_data['data']) > 0:
            # 更新现有记录
            product_id = existing_data['data'][0].get('id')
            update_url = f"{BASE_API_URL}/items/product_data/{product_id}"
            response = requests.patch(update_url, headers=get_headers(), json=product_data)

            if response.status_code in [200, 204]:
                print(f"成功更新产品数据: {goods_id}")
                return True
            else:
                print(f"更新产品数据时出错: {response.status_code}")
                return False
        else:
            # 创建新记录
            create_url = f"{BASE_API_URL}/items/product_data"
            response = requests.post(create_url, headers=get_headers(), json=product_data)

            if response.status_code in [200, 201, 204]:
                print(f"成功创建新产品数据: {goods_id}")
                return True
            else:
                print(f"创建产品数据时出错: {response.status_code}")
                return False
    except Exception as e:
        print(f"保存产品数据时发生异常: {str(e)}")
        traceback.print_exc()
        return False

# 删除日志记录函数，简化代码

def fetch_shop_ids():
    """从数据库获取所有商店ID并去重"""
    url = f"{BASE_API_URL}/items/shop_data"
    params = {
        "fields": "mall_id"
    }

    print(f"请求商店ID列表: {url}")
    try:
        response = requests.get(url, headers=get_headers(), params=params)
        print(f"获取商店ID状态码: {response.status_code}")

        if response.status_code != 200:
            print(f"获取商店ID时出错: {response.status_code}")
            return []

        data = response.json()

        # 从响应数据中提取全部mall_id
        all_shop_ids = [item.get('mall_id') for item in data.get('data', []) if item.get('mall_id')]
        print(f"获取到的全部mall_id数量: {len(all_shop_ids)}")

        # 使用集合进行去重
        unique_shop_ids = list(set(all_shop_ids))
        print(f"去重后的mall_id数量: {len(unique_shop_ids)}")

        return unique_shop_ids
    except Exception as e:
        print(f"获取商店ID时发生异常: {str(e)}")
        traceback.print_exc()
        return []

def process_shop_data(result):
    """处理并提取商店数据"""
    current_time = get_hk_time()

    print("处理商店数据...")

    # 提取商店数据的路径可能不同，尝试多种可能的结构
    shop_data = None

    if "result" in result:
        shop_info = result["result"]

        # 检查必要字段是否存在
        if "mall_id" in shop_info and "mall_name" in shop_info:
            # 检查必要数据不为空
            if shop_info["mall_id"] and shop_info["mall_name"]:
                # 将数值字段转换为整数
                goods_sales_num = convert_to_int(shop_info.get("goods_sales_num", "0"))
                goods_num = convert_to_int(shop_info.get("goods_num", "0"))
                review_num = convert_to_int(shop_info.get("review_num", "0"))

                shop_data = {
                    "update_time": current_time,
                    "mall_id": str(shop_info.get("mall_id", "")),
                    "mall_name": shop_info.get("mall_name", ""),
                    "mall_logo": shop_info.get("mall_logo", ""),
                    "goods_sales_num": goods_sales_num,
                    "goods_num": goods_num,
                    "review_num": review_num
                }
    else:
        # 尝试其他可能的数据结构
        if "data" in result and "mall_info" in result["data"]:
            shop_info = result["data"]["mall_info"]

            # 检查必要字段是否存在
            if "mall_id" in shop_info and "mall_name" in shop_info:
                # 检查必要数据不为空
                if shop_info["mall_id"] and shop_info["mall_name"]:
                    # 将数值字段转换为整数
                    goods_sales_num = convert_to_int(shop_info.get("goods_sales_num", "0"))
                    goods_num = convert_to_int(shop_info.get("goods_num", "0"))
                    review_num = convert_to_int(shop_info.get("review_num", "0"))

                    shop_data = {
                        "update_time": current_time,
                        "mall_id": str(shop_info.get("mall_id", "")),
                        "mall_name": shop_info.get("mall_name", ""),
                        "mall_logo": shop_info.get("mall_logo", ""),
                        "goods_sales_num": goods_sales_num,
                        "goods_num": goods_num,
                        "review_num": review_num
                    }

    # 最终检查，确保所有字段均有值，避免空数据
    if shop_data:
        # 验证所有数据字段
        for key, value in shop_data.items():
            if value is None or (isinstance(value, str) and value == ""):
                if key == "update_time":
                    shop_data[key] = current_time
                elif key == "mall_id" or key == "mall_name":
                    print(f"错误: 必要字段 {key} 为空，无法保存商店数据")
                    return None
                elif key in ["goods_sales_num", "goods_num", "review_num"]:
                    shop_data[key] = 0
                else:
                    shop_data[key] = ""

        return shop_data
    else:
        print("错误: 无法从API响应中提取商店数据")
        return None

def save_shop_data(shop_data):
    """将商店数据保存到数据库"""
    if not shop_data:
        print("警告: 商店数据为空，跳过保存")
        return False

    mall_id = shop_data.get('mall_id')
    if not mall_id:
        print("错误: 商店数据缺少mall_id，跳过保存")
        return False

    print(f"保存商店数据: mall_id={mall_id}")
    try:
        # 检查该mall_id是否已存在
        check_url = f"{BASE_API_URL}/items/shop_data?filter[mall_id][_eq]={mall_id}"
        check_response = requests.get(check_url, headers=get_headers())

        if check_response.status_code != 200:
            print(f"检查商店数据时出错: {check_response.status_code}")
            return False

        existing_data = check_response.json()

        if existing_data and existing_data.get('data') and len(existing_data['data']) > 0:
            # 更新现有记录
            shop_id = existing_data['data'][0].get('id')
            update_url = f"{BASE_API_URL}/items/shop_data/{shop_id}"
            response = requests.patch(update_url, headers=get_headers(), json=shop_data)

            if response.status_code in [200, 204]:
                print(f"成功更新商店数据: {mall_id}")
                return True
            else:
                print(f"更新商店数据时出错: {response.status_code}")
                return False
        else:
            # 创建新记录
            create_url = f"{BASE_API_URL}/items/shop_data"
            response = requests.post(create_url, headers=get_headers(), json=shop_data)

            if response.status_code in [200, 201, 204]:
                print(f"成功创建新商店数据: {mall_id}")
                return True
            else:
                print(f"创建商店数据时出错: {response.status_code}")
                return False
    except Exception as e:
        print(f"保存商店数据时发生异常: {str(e)}")
        traceback.print_exc()
        return False

def process_products(result, mall_id):
    """处理和提取产品数据"""
    current_time = get_hk_time()

    print("处理产品数据...")
    products_data = []

    # 尝试从多种可能的结构中提取产品列表
    goods_list = None

    if "result" in result and "data" in result["result"] and "goods_list" in result["result"]["data"]:
        goods_list = result["result"]["data"]["goods_list"]
    elif "data" in result and "goods_list" in result["data"]:
        goods_list = result["data"]["goods_list"]

    if goods_list:
        print(f"找到{len(goods_list)}个产品")

        for index, product in enumerate(goods_list):
            try:
                # 检查必要字段
                if "goods_id" not in product or not product["goods_id"]:
                    print(f"警告: 产品 #{index+1} 缺少goods_id，跳过")
                    continue

                # 提取价格，处理可能的不同结构，并转换为美元格式
                price = 0
                if "price_info" in product and "price" in product["price_info"]:
                    # 将价格从美分转换为美元（除以100）
                    price_cents = product["price_info"]["price"]
                    price = price_cents / 100.0

                # 提取图片URL
                image_url = ""
                if "image" in product and "url" in product["image"]:
                    image_url = product["image"]["url"]
                elif "thumb_url" in product:
                    image_url = product["thumb_url"]

                # 提取销售数量并转换为整数
                sales_num = 0
                if "sales_tip" in product:
                    sales_tip = product["sales_tip"]
                    if isinstance(sales_tip, str):
                        sales_num = extract_number_from_string(sales_tip)
                elif "sales_tip_text" in product and len(product["sales_tip_text"]) > 0:
                    sales_num = extract_number_from_string(product["sales_tip_text"][0])

                # 提取评论数并转换为整数
                comment_num = 0
                if "comment" in product:
                    comment_data = product["comment"]
                    if isinstance(comment_data, dict) and "comment_num_tips" in comment_data:
                        comment_num = extract_number_from_string(comment_data["comment_num_tips"])

                # 构建产品数据
                product_data = {
                    "update_time": current_time,
                    "mall_id": mall_id,
                    "goods_id": str(product.get("goods_id", "")),
                    "title": product.get("title", ""),
                    "image_url": image_url,
                    "sales_num": sales_num,
                    "price": price,
                    "comment": comment_num
                }

                # 验证所有字段都有值
                for key, value in product_data.items():
                    if value is None or (isinstance(value, str) and value == ""):
                        if key == "update_time":
                            product_data[key] = current_time
                        elif key == "goods_id" or key == "mall_id":
                            # 这些是必要字段，如果为空，跳过此产品
                            print(f"错误: 产品 #{index+1} 必要字段 {key} 为空，跳过此产品")
                            break
                        elif key in ["sales_num", "price", "comment"]:
                            product_data[key] = 0
                        else:
                            product_data[key] = ""

                if product_data["goods_id"] and product_data["mall_id"]:
                    products_data.append(product_data)
            except Exception as e:
                print(f"处理产品时出错: {str(e)}")
                traceback.print_exc()
    else:
        print("无法在响应中找到商品列表")

    return products_data



def main():
    """运行数据抓取过程的主函数"""
    print("="*50)
    print("开始运行Temu数据抓取")
    print(f"时间: {get_hk_time()}")
    print("="*50)

    # 获取所有商店ID
    print("\n获取商店ID列表...")
    shop_ids = fetch_shop_ids()

    # 如果没有找到商店ID，使用默认测试ID
    if not shop_ids:
        shop_ids = ["6313567470795"]  # 默认测试商店ID
        print(f"没有找到商店ID，使用测试ID: {shop_ids}")

    total_shops = len(shop_ids)
    successful_shops = 0
    successful_products = 0

    for index, mall_id in enumerate(shop_ids, 1):
        print("\n" + "="*50)
        print(f"正在处理商店 {index}/{total_shops}: mall_id {mall_id}")
        print("="*50)

        try:
            # 从Temu API获取数据
            result = fetch_temu_shop_data(mall_id)
            if not result:
                print(f"由于API错误，跳过mall_id为{mall_id}的商店")
                continue

            # 处理并保存商店数据
            shop_data = process_shop_data(result)
            if shop_data and save_shop_data(shop_data):
                successful_shops += 1

            # 处理并保存产品数据
            products = process_products(result, mall_id)
            print(f"为mall_id {mall_id}找到{len(products)}个产品")

            saved_products = 0
            for i, product in enumerate(products, 1):
                print(f"正在保存产品 {i}/{len(products)} (goods_id: {product['goods_id']})")
                if save_product_data(product):
                    saved_products += 1
                    successful_products += 1

            print(f"商店 {mall_id} 处理完成: 成功保存 {saved_products}/{len(products)} 个产品")

            # 商店间延迟60秒
            if index < total_shops:
                print(f"等待 {SHOP_DELAY} 秒后处理下一个商店...")
                time.sleep(SHOP_DELAY)

        except Exception as e:
            print(f"处理商店 {mall_id} 时发生异常: {str(e)}")
            traceback.print_exc()

    # 输出汇总信息
    print("\n" + "="*50)
    print("Temu数据抓取汇总")
    print(f"处理时间: {get_hk_time()}")
    print(f"处理商店总数: {total_shops}")
    print(f"成功保存商店数: {successful_shops}")
    print(f"成功保存产品数: {successful_products}")
    print("="*50)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"程序执行过程中发生未捕获的异常: {str(e)}")
        traceback.print_exc()