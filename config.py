# Temu API 配置
TEMU_API_CONFIG = {
    "BASE_URL": "https://www.temu.com",
    "API_PATH": "/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList",
    "DEFAULT_MALL_ID": "6313567470795",  # 默认测试商店ID
    "PAGE_SIZE": 8,
    "REQUEST_DELAY": 0.2,  # 请求间隔（秒）
    "SHOP_DELAY": 1,  # 商店间隔（秒）
}

# 后端 API 配置
BACKEND_API_CONFIG = {
    "BASE_URL": "http://172.25.165.28:8055",
    "API_TOKEN": "OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM",
}

# 时区配置
TIMEZONE_CONFIG = {
    "DEFAULT_TIMEZONE": "Asia/Hong_Kong"
}

# 请求头配置
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1'
}