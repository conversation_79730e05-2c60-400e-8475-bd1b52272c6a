{"test_time": "2025-08-01 10:20:17", "proxy_config": null, "results": {"connectivity": {"https://www.temu.com": {"status": "success", "status_code": 200, "response_time": 3.272777}, "https://api.temu.com": {"status": "success", "status_code": 403, "response_time": 1.600763}, "https://www.google.com": {"status": "success", "status_code": 200, "response_time": 1.475155}}, "api_test": {"status_code": 403, "response_time": 0.617436408996582, "content_length": 54, "headers": {"Server": "nginx", "Date": "Fri, 01 Aug 2025 02:20:17 GMT", "Content-Type": "application/json", "Content-Length": "54", "Connection": "keep-alive", "x-gateway-request-id": "1754014817918-17f46050d7ec822da05623a83a56c184-20", "Access-Control-Allow-Origin": "https://www.temu.com", "Vary": "Origin", "Access-Control-Allow-Headers": "Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, <PERSON>ie, AccessToken, PASSID, VerifyAuthToken, Anti-Content", "Access-Control-Allow-Methods": "GET, POST, OPTIONS, DELETE, PUT", "Access-Control-Allow-Credentials": "true", "strict-transport-security": "max-age=31536000", "content-security-policy-report-only": "default-src 'none';script-src 'report-sample';report-uri /api/sec-csp/110000007/sec-gif", "yak-timeinfo": "1754014817918|26", "Alt-Svc": "h3=\":443\"; ma=604800", "cip": "**************"}, "json_response": {"success": false, "error_code": 40001, "errorCode": 40001}}}}