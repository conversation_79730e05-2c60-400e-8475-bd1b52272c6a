#!/usr/bin/env python3
"""
Temu域名分析工具
分析访问Temu网站时需要的所有域名
"""

import requests
import re
import json
from urllib.parse import urlparse, urljoin
import time
from collections import set

class TemuDomainAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.domains = set()
        self.failed_domains = set()
        
        # 常见的Temu相关域名（基于经验和分析）
        self.known_temu_domains = [
            'www.temu.com',
            'temu.com',
            'api.temu.com',
            'static.temu.com',
            'img.temu.com',
            'cdn.temu.com',
            'assets.temu.com',
            'js.temu.com',
            'css.temu.com',
            'analytics.temu.com',
            'track.temu.com',
            'metrics.temu.com',
            'log.temu.com',
            'beacon.temu.com',
            'ws.temu.com',
            'socket.temu.com',
            'push.temu.com',
            'notification.temu.com',
            'payment.temu.com',
            'checkout.temu.com',
            'auth.temu.com',
            'login.temu.com',
            'account.temu.com',
            'user.temu.com',
            'profile.temu.com',
            'search.temu.com',
            'recommend.temu.com',
            'suggest.temu.com',
            'cart.temu.com',
            'order.temu.com',
            'shipping.temu.com',
            'review.temu.com',
            'rating.temu.com',
            'feedback.temu.com',
            'support.temu.com',
            'help.temu.com',
            'faq.temu.com',
            'live.temu.com',
            'stream.temu.com',
            'video.temu.com',
            'upload.temu.com',
            'download.temu.com',
            'file.temu.com',
            'storage.temu.com',
            'backup.temu.com',
            'cache.temu.com',
            'edge.temu.com',
            'global.temu.com',
            'international.temu.com',
            'mobile.temu.com',
            'app.temu.com',
            'wap.temu.com',
            'm.temu.com',
            'admin.temu.com',
            'seller.temu.com',
            'merchant.temu.com',
            'vendor.temu.com',
            'supplier.temu.com',
            'partner.temu.com',
            'affiliate.temu.com',
            'ads.temu.com',
            'advertising.temu.com',
            'promotion.temu.com',
            'campaign.temu.com',
            'marketing.temu.com',
            'email.temu.com',
            'newsletter.temu.com',
            'sms.temu.com',
            'message.temu.com',
            'chat.temu.com',
            'service.temu.com',
            'customer.temu.com',
            'client.temu.com',
            'gateway.temu.com',
            'proxy.temu.com',
            'load-balancer.temu.com',
            'lb.temu.com',
            'cluster.temu.com',
            'node.temu.com',
            'server.temu.com',
            'host.temu.com',
            'instance.temu.com',
            'container.temu.com',
            'docker.temu.com',
            'k8s.temu.com',
            'kubernetes.temu.com',
            'cloud.temu.com',
            'aws.temu.com',
            'azure.temu.com',
            'gcp.temu.com',
            'cdn1.temu.com',
            'cdn2.temu.com',
            'cdn3.temu.com',
            'static1.temu.com',
            'static2.temu.com',
            'static3.temu.com',
            'img1.temu.com',
            'img2.temu.com',
            'img3.temu.com',
            'assets1.temu.com',
            'assets2.temu.com',
            'assets3.temu.com',
            'js1.temu.com',
            'js2.temu.com',
            'js3.temu.com',
            'css1.temu.com',
            'css2.temu.com',
            'css3.temu.com',
            'api1.temu.com',
            'api2.temu.com',
            'api3.temu.com',
            'v1.temu.com',
            'v2.temu.com',
            'v3.temu.com',
            'beta.temu.com',
            'staging.temu.com',
            'test.temu.com',
            'dev.temu.com',
            'sandbox.temu.com',
            'demo.temu.com',
            'preview.temu.com',
            'pre.temu.com',
            'prod.temu.com',
            'production.temu.com',
            'live-prod.temu.com',
            'us.temu.com',
            'eu.temu.com',
            'asia.temu.com',
            'cn.temu.com',
            'jp.temu.com',
            'kr.temu.com',
            'sg.temu.com',
            'hk.temu.com',
            'tw.temu.com',
            'in.temu.com',
            'au.temu.com',
            'ca.temu.com',
            'uk.temu.com',
            'de.temu.com',
            'fr.temu.com',
            'es.temu.com',
            'it.temu.com',
            'br.temu.com',
            'mx.temu.com',
            'ar.temu.com',
            'cl.temu.com',
            'co.temu.com',
            'pe.temu.com',
            'za.temu.com',
            'eg.temu.com',
            'ma.temu.com',
            'ng.temu.com',
            'ke.temu.com',
            'gh.temu.com',
            'tz.temu.com',
            'ug.temu.com',
            'rw.temu.com',
            'et.temu.com',
            'mz.temu.com',
            'zw.temu.com',
            'bw.temu.com',
            'na.temu.com',
            'sz.temu.com',
            'ls.temu.com',
            'mw.temu.com',
            'zm.temu.com',
            'ao.temu.com',
            'cd.temu.com',
            'cg.temu.com',
            'cm.temu.com',
            'ga.temu.com',
            'gq.temu.com',
            'st.temu.com',
            'td.temu.com',
            'cf.temu.com',
            'dj.temu.com',
            'er.temu.com',
            'so.temu.com',
            'ss.temu.com',
            'sd.temu.com',
            'ly.temu.com',
            'tn.temu.com',
            'dz.temu.com',
            'mr.temu.com',
            'ml.temu.com',
            'bf.temu.com',
            'ne.temu.com',
            'sn.temu.com',
            'gm.temu.com',
            'gw.temu.com',
            'gn.temu.com',
            'sl.temu.com',
            'lr.temu.com',
            'ci.temu.com',
            'gh.temu.com',
            'tg.temu.com',
            'bj.temu.com',
            'ng.temu.com',
            'cv.temu.com',
            'st.temu.com',
            'gq.temu.com',
            'ga.temu.com',
            'cg.temu.com',
            'cd.temu.com',
            'ao.temu.com',
            'zm.temu.com',
            'mw.temu.com',
            'tz.temu.com',
            'ke.temu.com',
            'ug.temu.com',
            'rw.temu.com',
            'bi.temu.com',
            'et.temu.com',
            'dj.temu.com',
            'so.temu.com',
            'er.temu.com',
            'sd.temu.com',
            'ss.temu.com',
            'ly.temu.com',
            'eg.temu.com',
            'tn.temu.com',
            'dz.temu.com',
            'ma.temu.com',
            'eh.temu.com',
            'mr.temu.com',
            'ml.temu.com',
            'sn.temu.com',
            'gm.temu.com',
            'gw.temu.com',
            'gn.temu.com',
            'sl.temu.com',
            'lr.temu.com',
            'ci.temu.com',
            'bf.temu.com',
            'gh.temu.com',
            'tg.temu.com',
            'bj.temu.com',
            'ne.temu.com',
            'ng.temu.com',
            'td.temu.com',
            'cf.temu.com',
            'cm.temu.com',
            'cv.temu.com'
        ]
        
        # 第三方服务域名（Temu可能使用的）
        self.third_party_domains = [
            # Google服务
            'www.google.com',
            'apis.google.com',
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'www.googletagmanager.com',
            'www.google-analytics.com',
            'googleads.g.doubleclick.net',
            'stats.g.doubleclick.net',
            'www.googleadservices.com',
            'googletagservices.com',
            'pagead2.googlesyndication.com',
            'tpc.googlesyndication.com',
            'www.gstatic.com',
            'ssl.gstatic.com',
            'csi.gstatic.com',
            'maps.googleapis.com',
            'maps.gstatic.com',
            'translate.googleapis.com',
            'recaptcha.google.com',
            'www.recaptcha.net',
            
            # Facebook/Meta
            'www.facebook.com',
            'connect.facebook.net',
            'graph.facebook.com',
            'www.instagram.com',
            'scontent.xx.fbcdn.net',
            'static.xx.fbcdn.net',
            
            # CDN服务
            'cdnjs.cloudflare.com',
            'cdn.jsdelivr.net',
            'unpkg.com',
            'ajax.googleapis.com',
            'code.jquery.com',
            'stackpath.bootstrapcdn.com',
            'maxcdn.bootstrapcdn.com',
            'use.fontawesome.com',
            'kit.fontawesome.com',
            
            # 分析和追踪
            'www.googletagmanager.com',
            'bat.bing.com',
            'analytics.twitter.com',
            'px.ads.linkedin.com',
            'insight.adsrvr.org',
            'match.adsystem.com',
            'amazon-adsystem.com',
            'fls-na.amazon.com',
            'fls-eu.amazon.com',
            'fls-fe.amazon.com',
            
            # 支付服务
            'js.stripe.com',
            'checkout.stripe.com',
            'api.stripe.com',
            'www.paypal.com',
            'www.paypalobjects.com',
            'c.paypal.com',
            't.paypal.com',
            'www.sandbox.paypal.com',
            
            # 云服务
            'amazonaws.com',
            'cloudfront.net',
            's3.amazonaws.com',
            'azureedge.net',
            'blob.core.windows.net',
            'storage.googleapis.com',
            'firebaseapp.com',
            'firebaseio.com',
            
            # 其他常见服务
            'www.youtube.com',
            'i.ytimg.com',
            'player.vimeo.com',
            'vimeocdn.com',
            'platform.twitter.com',
            'syndication.twitter.com',
            'cdn.twitter.com',
            'abs.twimg.com',
            'pbs.twimg.com',
            'ton.twimg.com',
            'video.twimg.com',
            
            # 安全和验证
            'hcaptcha.com',
            'assets.hcaptcha.com',
            'api.hcaptcha.com',
            'newassets.hcaptcha.com',
            'imgs.hcaptcha.com',
            
            # 邮件服务
            'mailchimp.com',
            'list-manage.com',
            'sendgrid.net',
            'sendgrid.com',
            'mandrillapp.com',
            
            # 客服聊天
            'widget.intercom.io',
            'js.intercomcdn.com',
            'static.intercomcdn.com',
            'uploads.intercomcdn.com',
            'downloads.intercomcdn.com',
            'messenger.intercom.io',
            'api-iam.intercom.io',
            'api.intercom.io',
            'nexus-websocket-a.intercom.io',
            'nexus-websocket-b.intercom.io',
            'wss.intercom.io',
            'ping.intercom.io',
            'via.intercom.io',
            'gifs.intercom.io',
            'emoji.intercom.io',
            'help-assets.intercom.io',
            'help-widget.intercom.io',
            'help-messenger.intercom.io',
            'help-api.intercom.io',
            'help-nexus.intercom.io',
            'help-ping.intercom.io',
            'help-via.intercom.io',
            'help-gifs.intercom.io',
            'help-emoji.intercom.io',
            'help-assets-prod.intercom.io',
            'help-widget-prod.intercom.io',
            'help-messenger-prod.intercom.io',
            'help-api-prod.intercom.io',
            'help-nexus-prod.intercom.io',
            'help-ping-prod.intercom.io',
            'help-via-prod.intercom.io',
            'help-gifs-prod.intercom.io',
            'help-emoji-prod.intercom.io'
        ]
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }

    def test_domain_connectivity(self, domain, timeout=10):
        """测试域名连通性"""
        try:
            url = f"https://{domain}"
            response = self.session.head(url, headers=self.headers, timeout=timeout, allow_redirects=True)
            return True, response.status_code
        except requests.exceptions.SSLError:
            try:
                url = f"http://{domain}"
                response = self.session.head(url, headers=self.headers, timeout=timeout, allow_redirects=True)
                return True, response.status_code
            except:
                return False, None
        except:
            return False, None

    def analyze_domains(self):
        """分析所有可能的域名"""
        print("开始分析Temu相关域名...")
        print(f"总共需要测试 {len(self.known_temu_domains + self.third_party_domains)} 个域名")
        
        all_domains = self.known_temu_domains + self.third_party_domains
        
        for i, domain in enumerate(all_domains, 1):
            print(f"[{i}/{len(all_domains)}] 测试域名: {domain}")
            
            is_accessible, status_code = self.test_domain_connectivity(domain)
            
            if is_accessible:
                self.domains.add(domain)
                print(f"  ✓ 可访问 (状态码: {status_code})")
            else:
                self.failed_domains.add(domain)
                print(f"  ✗ 不可访问")
            
            # 避免请求过于频繁
            time.sleep(0.5)
        
        return self.domains

    def save_results(self, filename="temu_domains.json"):
        """保存分析结果"""
        results = {
            "accessible_domains": list(self.domains),
            "failed_domains": list(self.failed_domains),
            "total_tested": len(self.domains) + len(self.failed_domains),
            "accessible_count": len(self.domains),
            "failed_count": len(self.failed_domains),
            "analysis_time": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n分析结果已保存到: {filename}")
        return results

    def print_summary(self):
        """打印分析摘要"""
        print("\n" + "="*60)
        print("Temu域名分析摘要")
        print("="*60)
        print(f"可访问域名数量: {len(self.domains)}")
        print(f"不可访问域名数量: {len(self.failed_domains)}")
        print(f"总测试域名数量: {len(self.domains) + len(self.failed_domains)}")
        
        print("\n可访问的域名列表:")
        print("-" * 40)
        for domain in sorted(self.domains):
            print(f"  • {domain}")
        
        print(f"\n建议代理配置的域名 (共{len(self.domains)}个):")
        print("-" * 40)
        for domain in sorted(self.domains):
            print(f"*.{domain}")

def main():
    analyzer = TemuDomainAnalyzer()
    
    try:
        # 分析域名
        accessible_domains = analyzer.analyze_domains()
        
        # 打印摘要
        analyzer.print_summary()
        
        # 保存结果
        results = analyzer.save_results()
        
        print(f"\n分析完成！发现 {len(accessible_domains)} 个可访问的域名。")
        
    except KeyboardInterrupt:
        print("\n\n分析被用户中断")
        analyzer.print_summary()
        analyzer.save_results("temu_domains_partial.json")
    except Exception as e:
        print(f"\n分析过程中发生错误: {str(e)}")
        analyzer.print_summary()
        analyzer.save_results("temu_domains_error.json")

if __name__ == "__main__":
    main()
