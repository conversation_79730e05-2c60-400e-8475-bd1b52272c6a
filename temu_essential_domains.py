#!/usr/bin/env python3
"""
Temu核心域名检测工具
专门检测你的爬虫程序需要的核心域名
"""

import requests
import json
import time
from urllib.parse import urlparse

class TemuEssentialDomains:
    def __init__(self):
        self.session = requests.Session()
        
        # 基于你的爬虫代码和常见电商网站架构的核心域名
        self.essential_domains = [
            # 主域名
            'www.temu.com',
            'temu.com',
            
            # API相关
            'api.temu.com',
            'gateway.temu.com',
            
            # 静态资源
            'static.temu.com',
            'assets.temu.com',
            'cdn.temu.com',
            'img.temu.com',
            'images.temu.com',
            
            # 常见的CDN子域名
            'cdn1.temu.com',
            'cdn2.temu.com',
            'static1.temu.com',
            'static2.temu.com',
            'img1.temu.com',
            'img2.temu.com',
            
            # 地区相关
            'us.temu.com',
            'global.temu.com',
            
            # 可能的第三方服务
            'www.google.com',
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'www.googletagmanager.com',
            'www.google-analytics.com',
            'cdnjs.cloudflare.com',
            'ajax.googleapis.com',
            
            # 可能的云服务
            'amazonaws.com',
            'cloudfront.net',
            's3.amazonaws.com',
            
            # 安全相关
            'recaptcha.google.com',
            'www.recaptcha.net'
        ]
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        }
        
        self.accessible_domains = []
        self.failed_domains = []

    def test_domain(self, domain, timeout=10):
        """测试单个域名的连通性"""
        protocols = ['https', 'http']
        
        for protocol in protocols:
            try:
                url = f"{protocol}://{domain}"
                response = self.session.head(url, headers=self.headers, timeout=timeout, allow_redirects=True)
                return True, response.status_code, protocol
            except requests.exceptions.SSLError:
                continue
            except requests.exceptions.ConnectionError:
                continue
            except requests.exceptions.Timeout:
                continue
            except Exception as e:
                continue
        
        return False, None, None

    def test_temu_api_endpoint(self):
        """专门测试你的爬虫使用的API端点"""
        api_url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
        
        print(f"测试API端点: {api_url}")
        
        try:
            # 使用你代码中的headers
            api_headers = {
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
                "content-type": "application/json;charset=UTF-8",
                "origin": "https://www.temu.com",
                "referer": "https://www.temu.com/",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"
            }
            
            # 使用你代码中的payload
            payload = {
                "mallId": "6313567470795",  # 测试用的mall_id
                "mainGoodsIds": ["1"],
                "source_page_sn": "10013",
                "mall_id": "6313567470795",
                "main_goods_ids": ["1"],
                "filter_items": "",
                "page_number": 1,
                "page_size": 8,
                "list_id": "r7oe7gyw0vd5xo2z2qja2",
                "scene_code": "mall_rule",
                "page_sn": 10040,
                "page_el_sn": 201265,
                "source": 10018,
                "anti_content": "1"
            }
            
            response = self.session.post(api_url, headers=api_headers, json=payload, timeout=15)
            
            print(f"  API状态码: {response.status_code}")
            if response.status_code == 200:
                print("  ✓ API端点可访问")
                return True
            else:
                print(f"  ✗ API端点返回错误: {response.status_code}")
                print(f"  响应内容: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"  ✗ API端点测试失败: {str(e)}")
            return False

    def analyze(self):
        """分析所有核心域名"""
        print("开始分析Temu核心域名...")
        print(f"总共需要测试 {len(self.essential_domains)} 个域名")
        print("="*60)
        
        for i, domain in enumerate(self.essential_domains, 1):
            print(f"[{i}/{len(self.essential_domains)}] 测试: {domain}")
            
            is_accessible, status_code, protocol = self.test_domain(domain)
            
            if is_accessible:
                self.accessible_domains.append({
                    'domain': domain,
                    'protocol': protocol,
                    'status_code': status_code
                })
                print(f"  ✓ 可访问 ({protocol}, 状态码: {status_code})")
            else:
                self.failed_domains.append(domain)
                print(f"  ✗ 不可访问")
            
            time.sleep(0.5)  # 避免请求过于频繁
        
        print("\n" + "="*60)
        print("测试Temu API端点...")
        print("="*60)
        
        api_accessible = self.test_temu_api_endpoint()
        
        return self.accessible_domains, api_accessible

    def generate_proxy_config(self):
        """生成代理配置建议"""
        print("\n" + "="*60)
        print("代理配置建议")
        print("="*60)
        
        print("\n1. 需要代理的核心域名:")
        print("-" * 40)
        for domain_info in self.accessible_domains:
            domain = domain_info['domain']
            print(f"  • {domain}")
        
        print("\n2. 通配符域名配置 (推荐):")
        print("-" * 40)
        unique_base_domains = set()
        for domain_info in self.accessible_domains:
            domain = domain_info['domain']
            if domain.startswith('www.'):
                base_domain = domain[4:]  # 去掉www.
            else:
                base_domain = domain
            unique_base_domains.add(base_domain)
        
        for base_domain in sorted(unique_base_domains):
            print(f"  *.{base_domain}")
        
        print("\n3. 具体的代理规则 (适用于大多数代理软件):")
        print("-" * 40)
        for domain_info in self.accessible_domains:
            domain = domain_info['domain']
            print(f"  DOMAIN-SUFFIX,{domain},PROXY")
        
        return list(unique_base_domains)

    def save_results(self, filename="temu_essential_domains.json"):
        """保存分析结果"""
        results = {
            "accessible_domains": self.accessible_domains,
            "failed_domains": self.failed_domains,
            "accessible_count": len(self.accessible_domains),
            "failed_count": len(self.failed_domains),
            "analysis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "proxy_domains": [info['domain'] for info in self.accessible_domains]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n分析结果已保存到: {filename}")
        return results

    def print_summary(self):
        """打印分析摘要"""
        print("\n" + "="*60)
        print("Temu核心域名分析摘要")
        print("="*60)
        print(f"可访问域名: {len(self.accessible_domains)}")
        print(f"不可访问域名: {len(self.failed_domains)}")
        print(f"成功率: {len(self.accessible_domains)/(len(self.accessible_domains)+len(self.failed_domains))*100:.1f}%")

def main():
    analyzer = TemuEssentialDomains()
    
    try:
        # 分析域名
        accessible_domains, api_accessible = analyzer.analyze()
        
        # 生成代理配置
        proxy_domains = analyzer.generate_proxy_config()
        
        # 打印摘要
        analyzer.print_summary()
        
        # 保存结果
        results = analyzer.save_results()
        
        print(f"\n✅ 分析完成！")
        print(f"发现 {len(accessible_domains)} 个可访问的核心域名")
        print(f"API端点状态: {'✓ 可访问' if api_accessible else '✗ 不可访问'}")
        
        if not api_accessible:
            print("\n⚠️  警告: API端点不可访问，这可能是导致你的爬虫被限制的原因")
            print("建议检查:")
            print("1. 网络连接")
            print("2. 代理设置")
            print("3. Cookie是否有效")
        
    except KeyboardInterrupt:
        print("\n\n分析被用户中断")
        analyzer.print_summary()
    except Exception as e:
        print(f"\n分析过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
