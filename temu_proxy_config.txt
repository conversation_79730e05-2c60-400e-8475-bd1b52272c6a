# Temu爬虫代理配置文件
# 基于域名分析结果，这些是你的爬虫需要代理的核心域名

# ===== 核心Temu域名 (必须代理) =====
# 主站域名
*.temu.com
www.temu.com

# API相关域名 (你的爬虫直接使用)
api.temu.com
gateway.temu.com

# 静态资源域名
static.temu.com
assets.temu.com
cdn.temu.com
img.temu.com
images.temu.com

# CDN子域名
cdn1.temu.com
cdn2.temu.com
static1.temu.com
static2.temu.com
img1.temu.com
img2.temu.com

# 地区相关域名
us.temu.com
global.temu.com

# ===== 第三方服务域名 (可选，但建议代理) =====
# Google服务 (用于字体、分析等)
*.google.com
*.googleapis.com
*.gstatic.com
*.google-analytics.com
*.googletagmanager.com
*.recaptcha.google.com
*.recaptcha.net

# CDN服务
*.cloudflare.com
cdnjs.cloudflare.com
ajax.googleapis.com

# AWS服务 (Temu可能使用)
*.amazonaws.com
*.cloudfront.net
s3.amazonaws.com

# ===== 针对不同代理软件的配置格式 =====

## Clash配置格式:
# rules:
#   - DOMAIN-SUFFIX,temu.com,PROXY
#   - DOMAIN-SUFFIX,api.temu.com,PROXY
#   - DOMAIN-SUFFIX,static.temu.com,PROXY
#   - DOMAIN-SUFFIX,cdn.temu.com,PROXY
#   - DOMAIN-SUFFIX,img.temu.com,PROXY
#   - DOMAIN-SUFFIX,us.temu.com,PROXY
#   - DOMAIN-SUFFIX,global.temu.com,PROXY
#   - DOMAIN-SUFFIX,google.com,PROXY
#   - DOMAIN-SUFFIX,googleapis.com,PROXY
#   - DOMAIN-SUFFIX,gstatic.com,PROXY
#   - DOMAIN-SUFFIX,cloudflare.com,PROXY
#   - DOMAIN-SUFFIX,amazonaws.com,PROXY
#   - DOMAIN-SUFFIX,cloudfront.net,PROXY

## V2Ray/Xray配置格式:
# "routing": {
#   "rules": [
#     {
#       "type": "field",
#       "domain": [
#         "temu.com",
#         "api.temu.com",
#         "static.temu.com",
#         "cdn.temu.com",
#         "img.temu.com",
#         "us.temu.com",
#         "global.temu.com",
#         "google.com",
#         "googleapis.com",
#         "gstatic.com",
#         "cloudflare.com",
#         "amazonaws.com",
#         "cloudfront.net"
#       ],
#       "outboundTag": "proxy"
#     }
#   ]
# }

## Shadowsocks配置格式:
# [Rule]
# DOMAIN-SUFFIX,temu.com,Proxy
# DOMAIN-SUFFIX,api.temu.com,Proxy
# DOMAIN-SUFFIX,static.temu.com,Proxy
# DOMAIN-SUFFIX,cdn.temu.com,Proxy
# DOMAIN-SUFFIX,img.temu.com,Proxy
# DOMAIN-SUFFIX,us.temu.com,Proxy
# DOMAIN-SUFFIX,global.temu.com,Proxy
# DOMAIN-SUFFIX,google.com,Proxy
# DOMAIN-SUFFIX,googleapis.com,Proxy
# DOMAIN-SUFFIX,gstatic.com,Proxy
# DOMAIN-SUFFIX,cloudflare.com,Proxy
# DOMAIN-SUFFIX,amazonaws.com,Proxy
# DOMAIN-SUFFIX,cloudfront.net,Proxy

## PAC文件格式:
# function FindProxyForURL(url, host) {
#     if (shExpMatch(host, "*.temu.com") ||
#         shExpMatch(host, "*.google.com") ||
#         shExpMatch(host, "*.googleapis.com") ||
#         shExpMatch(host, "*.gstatic.com") ||
#         shExpMatch(host, "*.cloudflare.com") ||
#         shExpMatch(host, "*.amazonaws.com") ||
#         shExpMatch(host, "*.cloudfront.net")) {
#         return "PROXY your-proxy-server:port";
#     }
#     return "DIRECT";
# }

# ===== 重要提示 =====
# 1. 你的API端点 https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList 返回403错误
# 2. 这表明Temu检测到了你的请求并进行了限制
# 3. 建议措施:
#    - 使用高质量的住宅代理IP
#    - 轮换User-Agent
#    - 添加随机延迟
#    - 检查和更新Cookie
#    - 模拟真实浏览器行为

# ===== 最小化配置 (仅核心域名) =====
# 如果你只想代理最核心的域名，使用以下配置:
# *.temu.com
# *.google.com
# *.googleapis.com
# *.amazonaws.com

# ===== 测试建议 =====
# 1. 先配置 *.temu.com 的代理
# 2. 测试你的爬虫是否能正常访问
# 3. 如果仍有问题，逐步添加其他域名
# 4. 监控网络请求，看是否还有其他域名需要代理
