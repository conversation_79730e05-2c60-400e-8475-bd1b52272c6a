<script setup lang="ts">
// 移除了HelloWorld导入
import { onMounted } from 'vue';

onMounted(() => {
  // 打印当前环境信息
  console.log('环境信息:', {
    mode: import.meta.env.MODE,
    baseUrl: import.meta.env.BASE_URL,
    publicPath: import.meta.env.BASE_URL,
  });
});
</script>

<template>
  <router-view />
</template>

<style>
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

#app {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
  background-color: #f5f7fa;
  overflow: hidden;
  box-sizing: border-box;
}

/* 所有元素采用border-box盒模型 */
* {
  box-sizing: border-box;
}

/* 确保Arco Design组件占满宽度 */
.arco-layout {
  width: 100% !important;
  height: 100% !important;
}

.arco-layout-sider-children {
  width: 100% !important;
  height: 100% !important;
}

/* 强制表格和卡片组件优化 */
.arco-table, 
.arco-card {
  width: 100% !important;
  height: 100% !important;
  box-sizing: border-box !important;
}

.arco-card-body {
  width: 100% !important;
  padding: 16px !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 表格样式全局优化 */
.arco-table-container {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

.arco-table-content {
  flex: 1 !important;
  overflow: auto !important;
}

.arco-table-body {
  overflow: auto !important;
}

.arco-table-element {
  table-layout: fixed !important;
}

.arco-table-th,
.arco-table-td {
  box-sizing: border-box !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}

/* 优化表格滚动条 */
.arco-scrollbar__bar {
  display: block !important;
}

.arco-scrollbar__bar.is-horizontal {
  height: 6px !important;
}

.arco-scrollbar__bar.is-vertical {
  width: 6px !important;
}

.arco-scrollbar__thumb {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border-radius: 3px !important;
}

/* 操作按钮优化 */
.arco-btn {
  font-weight: 400 !important;
}

.arco-btn-size-small {
  padding: 0 8px !important;
  height: 28px !important;
  font-size: 13px !important;
}

/* 表格头部优化 */
.arco-table-th {
  background-color: #f2f3f5 !important;
  font-weight: 600 !important;
  color: #1d2129 !important;
}

/* 表格行交替色 */
.arco-table-stripe .arco-table-tr:nth-child(even) .arco-table-td {
  background-color: rgba(var(--primary-1), 0.02) !important;
}

/* 表格行hover效果 */
.arco-table-tr:hover .arco-table-td {
  background-color: rgba(var(--primary-2), 0.1) !important;
}

/* 分页居中显示 */
.arco-pagination {
  margin-top: 16px !important;
  justify-content: center !important;
}
</style>
