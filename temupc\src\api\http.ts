import axios from 'axios'

// 根据环境选择API地址
const getBaseUrl = () => {
  // 生产环境使用配置的外网地址
  if (import.meta.env.PROD) {
    return import.meta.env.VITE_API_URL || 'https://your-production-api-url.com'
  }
  // 开发环境使用本地地址
  return 'http://172.25.165.28:8055'
}

// 创建axios实例
const http = axios.create({
  baseURL: getBaseUrl(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// 请求拦截器
http.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      // 设置token到头部
      config.headers['Authorization'] = `Bearer ${token}`
    } else {
      // 直接使用API_TOKEN
      config.headers['Authorization'] = 'Bearer OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM'
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response?.status === 401) {
      // 未授权，跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default http 