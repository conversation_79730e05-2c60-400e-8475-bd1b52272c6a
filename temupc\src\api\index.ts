import http from './http'

// 用户登录
export const login = (data: { email: string; password: string }) => {
  return http.post('/auth/login', data)
}

// 获取店铺列表
export const getShopList = (params?: any) => {
  return http.get('/items/shop_data', { params })
}

// 获取店铺日志
export const getShopLog = (params?: any) => {
  return http.get('/items/shop_log', { params })
}

// 获取商品列表
export const getProductList = (params?: any) => {
  // 确保包含shop_data表的mall_id字段
  const newParams = { ...params };
  if (!newParams.fields) {
    newParams.fields = ['*', 'shop_data.mall_id'];
  } else if (!newParams.fields.includes('shop_data.mall_id')) {
    newParams.fields.push('shop_data.mall_id');
  }
  
  return http.get('/items/product_data', { params: newParams });
}

// 获取商品日志
export const getProductLog = (params?: any) => {
  return http.get('/items/product_log', { params })
}

// 根据店铺ID获取该店铺下的商品
export const getProductsByShopId = (shopId: number) => {
  return http.get('/items/product_data', {
    params: {
      filter: {
        shop_data: {
          _eq: shopId
        }
      },
      fields: ['*', 'shop_data.mall_id']
    }
  })
} 