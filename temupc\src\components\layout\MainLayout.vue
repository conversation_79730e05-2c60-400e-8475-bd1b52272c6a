<template>
  <div class="layout-container">
    <a-layout class="layout">
      <a-layout-header class="header">
        <div class="logo">店铺分析系统</div>
        <div class="header-right">
          <a-button type="text" @click="handleLogout">
            <template #icon><icon-export /></template>
            退出登录
          </a-button>
        </div>
      </a-layout-header>
      <a-layout class="main-layout">
        <a-layout-sider theme="light" collapsible :width="200" class="sider">
          <a-menu
            :default-active="activeMenu"
            @menu-item-click="onMenuItemClick"
            class="full-height-menu"
          >
            <a-menu-item key="shop">
              <template #icon><icon-apps /></template>
              店铺列表
            </a-menu-item>
            <a-menu-item key="all-products">
              <template #icon><icon-list /></template>
              所有商品
            </a-menu-item>
          </a-menu>
        </a-layout-sider>
        <a-layout-content class="content">
          <router-view />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { IconApps, IconExport, IconList } from '@arco-design/web-vue/es/icon';
import { useUserStore } from '../../store/user';
import { Message } from '@arco-design/web-vue';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const activeMenu = computed(() => {
  const path = route.path;
  if (path.includes('/shop')) return 'shop';
  if (path === '/product' || path.includes('/product/')) return 'all-products';
  return '';
});

const onMenuItemClick = (key: string) => {
  if (key === 'shop') {
    router.push('/shop');
  } else if (key === 'all-products') {
    router.push('/product');
  }
};

const handleLogout = () => {
  userStore.logout();
  Message.success('已退出登录');
  router.push('/login');
};
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.layout {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.main-layout {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex: 1;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  z-index: 100;
  height: 60px;
  flex-shrink: 0;
}

.logo {
  font-size: 18px;
  font-weight: bold;
  color: #165dff;
}

.header-right {
  display: flex;
  align-items: center;
}

.sider {
  height: 100%;
  border-right: 1px solid #e5e6eb;
  flex-shrink: 0;
}

.full-height-menu {
  height: 100%;
}

.content {
  padding: 16px;
  background-color: #f5f7fa;
  height: 100%;
  flex: 1;
  overflow: auto;
  position: relative;
  box-sizing: border-box;
}

/* 添加对内容区域的滚动控制 */
.content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.content::-webkit-scrollbar-thumb {
  background: #cdd0d6;
  border-radius: 4px;
}

.content::-webkit-scrollbar-track {
  background: #f5f7fa;
}

/* 确保页面内容不会溢出 */
:deep(.shop-container),
:deep(.product-container) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.arco-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.arco-card-body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
</style> 