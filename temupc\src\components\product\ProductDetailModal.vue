<template>
  <a-modal
    :visible="visible"
    @update:visible="emit('update:visible', $event)"
    :title="`销量明细 (商品ID: ${productId})`"
    width="800px"
    @cancel="handleClose"
    :footer="false"
    :mask-closable="false"
    :unmount-on-close="false"
    class="product-detail-modal"
  >
    <div class="product-detail-container">
      <div v-if="loading" class="loading-container">
        <a-spin tip="加载中..."></a-spin>
      </div>
      <div v-else-if="error" class="error-container">
        <a-result status="error" :subtitle="errorMessage">
          <template #extra>
            <a-button type="primary" @click="fetchProductDetails">
              重试
            </a-button>
          </template>
        </a-result>
      </div>
      <div v-else>
        <!-- 添加店铺信息卡片 -->
        <div class="shop-info-card">
          <div class="shop-info-header">商品基本信息</div>
          <div class="shop-info-content">
            <div class="shop-logo">
              <a-image-preview-group infinite>
                <a-image 
                  v-if="productImage" 
                  :src="productImage" 
                  width="100" 
                  height="100"
                  alt="商品图片"
                  fit="cover"
                  class="product-image"
                />
                <a-avatar v-else :size="100" shape="square">
                  <template #icon><icon-file-image /></template>
                </a-avatar>
              </a-image-preview-group>
            </div>
            <div class="shop-details">
              <div class="product-details-row">
                <span class="detail-label">商品名称:</span>
                <span class="detail-value">{{ productTitle || '未知商品' }}</span>
              </div>
              <div class="product-details-row">
                <span class="detail-label">所属店铺:</span>
                <span class="detail-value">{{ shopInfo?.name || '未知店铺' }}</span>
              </div>
              <div class="product-details-row">
                <span class="detail-label">店铺ID:</span>
                <span class="detail-value">{{ shopInfo?.id || '未知' }}</span>
              </div>
              <div class="product-details-row">
                <span class="detail-label">当前销量:</span>
                <span class="detail-value highlight-value">{{ formatNumber(latestSalesNum) }}</span>
              </div>
              <div class="product-details-row">
                <span class="detail-label">当前价格:</span>
                <span class="detail-value price-value">¥{{ formatPrice(latestPrice) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <a-divider style="margin: 16px 0">
          <template #default>
            <a-space>
              <icon-history />
              <span>销量历史记录</span>
            </a-space>
          </template>
        </a-divider>
        
        <div class="table-wrapper">
          <a-table
            :columns="columns"
            :data="detailData"
            stripe
            border="cell"
            size="small"
            :pagination="false"
            :loading="loading"
            row-key="id"
            :scroll="{ y: 200 }"
          >
            <template #update_time="{ record }">
              {{ formatDate(record.update_time) }}
            </template>
            
            <template #sales_num="{ record }">
              {{ formatNumber(record.sales_num) }}
            </template>
            
            <template #price="{ record }">
              <span class="price-value">¥{{ formatPrice(record.price) }}</span>
            </template>
            
            <template #new_orders="{ record }">
              <a-tag v-if="record.new_orders > 0" color="green">+{{ record.new_orders }}</a-tag>
              <a-tag v-else-if="record.new_orders < 0" color="red">{{ record.new_orders }}</a-tag>
              <span v-else>-</span>
            </template>
            
            <template #order_growth_rate="{ record }">
              <a-tag v-if="record.order_growth_rate > 0" color="green">+{{ formatPercent(record.order_growth_rate) }}</a-tag>
              <a-tag v-else-if="record.order_growth_rate < 0" color="red">{{ formatPercent(record.order_growth_rate) }}</a-tag>
              <span v-else>-</span>
            </template>
            
            <template #new_comments="{ record }">
              <a-tag v-if="record.new_comments > 0" color="green">+{{ record.new_comments }}</a-tag>
              <a-tag v-else-if="record.new_comments < 0" color="red">{{ record.new_comments }}</a-tag>
              <span v-else>-</span>
            </template>
            
            <template #comment_growth_rate="{ record }">
              <a-tag v-if="record.comment_growth_rate > 0" color="green">+{{ formatPercent(record.comment_growth_rate) }}</a-tag>
              <a-tag v-else-if="record.comment_growth_rate < 0" color="red">{{ formatPercent(record.comment_growth_rate) }}</a-tag>
              <span v-else>-</span>
            </template>
          </a-table>
        </div>
        
        <div class="modal-footer">
          <a-button @click="handleClose" size="medium">关闭</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconHistory, IconFileImage } from '@arco-design/web-vue/es/icon';
import http from '../../api/http';

const props = defineProps<{
  visible: boolean;
  productId: string;
}>();

const emit = defineEmits(['update:visible', 'close']);

// 错误信息计算属性
const errorMessage = computed(() => {
  if (!error.value) return '';
  return typeof error.value === 'string' ? error.value : '数据加载错误';
});

const columns = [
  { title: '采集时间', dataIndex: 'update_time', slotName: 'update_time', width: 130, align: 'center', sortable: true },
  { title: '销售量', dataIndex: 'sales_num', slotName: 'sales_num', width: 80, align: 'right', sortable: true },
  { title: '销售价格', dataIndex: 'price', slotName: 'price', width: 80, align: 'right', sortable: true },
  { title: '评论数', dataIndex: 'comment', width: 80, align: 'right', sortable: true },
  { title: '新增订单数', dataIndex: 'new_orders', slotName: 'new_orders', width: 90, align: 'center', sortable: true },
  { title: '环比订单增长', dataIndex: 'order_growth_rate', slotName: 'order_growth_rate', width: 100, align: 'center', sortable: true },
  { title: '新增评论数', dataIndex: 'new_comments', slotName: 'new_comments', width: 90, align: 'center', sortable: true },
  { title: '评论环比增长', dataIndex: 'comment_growth_rate', slotName: 'comment_growth_rate', width: 100, align: 'center', sortable: true }
];

const detailData = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);
const shopInfo = ref<{ id?: string; logo?: string; name?: string } | null>(null);
const productTitle = ref<string>('');
const productImage = ref<string>('');
const latestSalesNum = ref<number>(0);
const latestPrice = ref<number>(0);

// 监听productId和visible变化，加载商品明细数据
watch(
  () => [props.productId, props.visible],
  ([productId, visible]) => {
    if (productId && visible) {
      error.value = null;
      fetchProductDetails();
    }
  },
  { immediate: true }
);

// 获取商品明细数据
const fetchProductDetails = async () => {
  if (!props.productId) {
    error.value = '未提供有效的商品ID';
    return;
  }
  
  loading.value = true;
  error.value = null;
  shopInfo.value = null; // 重置店铺信息
  productTitle.value = '';
  productImage.value = '';
  console.log('开始获取商品明细数据，商品ID:', props.productId);
  
  try {
    // 首先查询产品数据，并包含shop_data关联表信息
    const productRes: any = await http.get('/items/product_data', {
      params: {
        filter: {
          goods_id: { _contains: props.productId } // 使用包含匹配，更宽松的匹配方式
        },
        fields: ['*', 'shop_data.mall_id', 'shop_data.mall_name', 'shop_data.mall_logo'], // 获取店铺相关字段
        sort: ['-update_time']
      }
    });
    
    console.log('查询到商品记录数量:', productRes.data?.length || 0);
    
    // 提取店铺信息和商品信息
    if (productRes.data && productRes.data.length > 0) {
      const firstProduct = productRes.data[0];
      
      // 设置商品标题和图片
      if (firstProduct.title) {
        productTitle.value = firstProduct.title;
      }
      
      if (firstProduct.image_url) {
        productImage.value = firstProduct.image_url;
      }
      
      // 设置最新销量和价格
      latestSalesNum.value = firstProduct.sales_num || 0;
      latestPrice.value = firstProduct.price || 0;
      
      if (typeof firstProduct.shop_data === 'object' && firstProduct.shop_data) {
        shopInfo.value = {
          id: firstProduct.shop_data.mall_id || '未知',
          logo: firstProduct.shop_data.mall_logo || '',
          name: firstProduct.shop_data.mall_name || '未知店铺'
        };
      }
    }
    
    // 保存产品数据的ID列表，用于查询日志
    let productIds: number[] = [];
    if (productRes.data && productRes.data.length > 0) {
      productIds = productRes.data
        .filter((item: any) => typeof item.id === 'number')
        .map((item: any) => item.id);
    }
    
    console.log('商品记录ID列表:', productIds);
    
    // 临时存储已处理的商品数据
    let processedProducts: any[] = [];
    
    if (productIds.length > 0) {
      // 获取所有相关产品日志
      const logRes: any = await http.get('/items/product_log', {
        params: {
          filter: {
            product_data: {
              _in: productIds
            }
          },
          sort: ['-log_time']
        }
      });
      
      console.log('查询到商品日志数量:', logRes.data?.length || 0);
      
      // 直接展示所有日志记录
      if (logRes.data && logRes.data.length > 0) {
        // 按照商品ID分组日志
        const logsByProductId: {[key: number]: any[]} = {};
        
        logRes.data.forEach((log: any) => {
          if (!logsByProductId[log.product_data]) {
            logsByProductId[log.product_data] = [];
          }
          logsByProductId[log.product_data].push(log);
        });
        
        console.log('按商品ID分组的日志:', logsByProductId);
        
        // 对每个产品数据，找到对应的所有日志
        productRes.data.forEach((product: any) => {
          if (typeof product.id === 'number') {
            const productLogs = logsByProductId[product.id] || [];
            // 直接从product中获取shop_data.mall_id
            const mallId = typeof product.shop_data === 'object' && product.shop_data ? 
                          product.shop_data.mall_id : '未知';
            
            if (productLogs.length > 0) {
              // 为每条日志创建一条完整的记录
              productLogs.forEach((log: any) => {
                processedProducts.push({
                  id: `${product.id}_${log.id}`, // 创建唯一标识
                  update_time: log.log_time || product.update_time,
                  goods_id: product.goods_id || props.productId, // 确保使用完整的商品ID
                  mall_id: mallId, // 使用获取到的mall_id
                  title: product.title,
                  sales_num: product.sales_num,
                  price: product.price,
                  comment: product.comment,
                  new_orders: log.order_increase || 0,
                  order_growth_rate: log.order_growth_rate || 0,
                  new_comments: log.comment_increase || 0,
                  comment_growth_rate: log.comment_growth_rate || 0
                });
              });
            } else {
              // 没有日志的产品也要显示
              processedProducts.push({
                ...product,
                goods_id: product.goods_id || props.productId, // 确保使用完整的商品ID
                mall_id: mallId, // 使用获取到的mall_id 
                new_orders: 0,
                order_growth_rate: 0,
                new_comments: 0,
                comment_growth_rate: 0
              });
            }
          }
        });
      } else {
        // 没有日志数据，只显示产品基本信息
        productRes.data.forEach((product: any) => {
          // 直接从product中获取shop_data.mall_id
          const mallId = typeof product.shop_data === 'object' && product.shop_data ? 
                        product.shop_data.mall_id : '未知';
                        
          processedProducts.push({
            ...product,
            goods_id: product.goods_id || props.productId, // 确保使用完整的商品ID
            mall_id: mallId, // 使用获取到的mall_id
            new_orders: 0,
            order_growth_rate: 0,
            new_comments: 0,
            comment_growth_rate: 0
          });
        });
      }
    } else {
      // 未找到相关产品记录
      error.value = `未找到商品ID为 ${props.productId} 的记录`;
    }
    
    // 按时间降序排序
    processedProducts.sort((a, b) => {
      const dateA = new Date(a.update_time).getTime();
      const dateB = new Date(b.update_time).getTime();
      return dateB - dateA;
    });
    
    detailData.value = processedProducts;
    
    console.log('最终展示的商品明细数据:', detailData.value);
    
    if (detailData.value.length === 0) {
      error.value = '商品数据为空';
    }
  } catch (err: any) {
    console.error('获取商品明细失败:', err);
    error.value = typeof err.message === 'string' ? err.message : '获取商品明细失败';
    Message.error('获取商品明细数据失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-';
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化数字
const formatNumber = (num: number) => {
  if (num === undefined || num === null) return '-';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num.toString();
};

// 格式化价格
const formatPrice = (price: number) => {
  if (price === undefined || price === null) return '-';
  return price.toFixed(2);
};

// 格式化百分比
const formatPercent = (val: number) => {
  if (val === undefined || val === null) return '-';
  return (val * 100).toFixed(2) + '%';
};

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
  // 清空数据
  detailData.value = [];
};
</script>

<style scoped>
.product-detail-container {
  max-height: 500px;
  overflow-y: auto;
  position: relative;
}

.shop-info-card {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.shop-info-header {
  font-size: 16px;
  font-weight: bold;
  color: #1d2129;
  margin-bottom: 12px;
}

.shop-info-content {
  display: flex;
  align-items: center;
}

.shop-logo {
  margin-right: 16px;
}

.shop-details {
  flex: 1;
}

.shop-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.shop-id {
  color: #86909c;
  font-size: 14px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

:deep(.arco-table-th) {
  white-space: nowrap;
  text-align: center !important;
  font-weight: bold !important;
  background-color: #f2f3f5 !important;
}

:deep(.arco-table-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 