<template>
  <a-modal
    :visible="visible"
    @update:visible="emit('update:visible', $event)"
    :title="`店铺明细 (店铺ID: ${shopId})`"
    width="800px"
    @cancel="handleClose"
  >
    <div class="shop-detail-container">
      <div v-if="loading" class="loading-container">
        <a-spin tip="加载中..."></a-spin>
      </div>
      <div v-else-if="error" class="error-container">
        <a-result status="error" :subtitle="errorMessage">
          <template #extra>
            <a-button type="primary" @click="fetchShopDetails(String(shopId))">
              重试
            </a-button>
          </template>
        </a-result>
      </div>
      <a-table
        v-else
        :columns="columns"
        :data="detailData"
        stripe
        border="cell"
        size="medium"
        :pagination="false"
        :loading="loading"
        row-key="id"
      >
        <template #update_time="{ record }">
          {{ formatDate(record.update_time) }}
        </template>
        
        <template #goods_sales_num="{ record }">
          {{ formatNumber(record.goods_sales_num) }}
        </template>
        
        <template #order_increase="{ record }">
          <a-tag v-if="record.order_increase > 0" color="green">+{{ record.order_increase }}</a-tag>
          <a-tag v-else-if="record.order_increase < 0" color="red">{{ record.order_increase }}</a-tag>
          <span v-else>-</span>
        </template>
        
        <template #growth_rate="{ record }">
          <a-tag v-if="record.growth_rate > 0" color="green">+{{ formatPercent(record.growth_rate) }}</a-tag>
          <a-tag v-else-if="record.growth_rate < 0" color="red">{{ formatPercent(record.growth_rate) }}</a-tag>
          <span v-else>-</span>
        </template>
      </a-table>
    </div>
    
    <template #footer>
      <a-button @click="handleClose">关闭</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import http from '../../api/http';

const props = defineProps<{
  visible: boolean;
  shopId: string;
}>();

const emit = defineEmits(['update:visible', 'close']);

// 错误信息计算属性
const errorMessage = computed(() => {
  if (!error.value) return '';
  return typeof error.value === 'string' ? error.value : '数据加载错误';
});

const columns = [
  { title: '更新时间', dataIndex: 'update_time', slotName: 'update_time', width: 150, align: 'center', sortable: true },
  { title: '店铺ID', dataIndex: 'mall_id', width: 150, align: 'center' },
  { title: '店铺名称', dataIndex: 'mall_name', width: 150, ellipsis: true },
  { title: '店铺销量', dataIndex: 'goods_sales_num', slotName: 'goods_sales_num', width: 100, align: 'right', sortable: true },
  { title: '商品数量', dataIndex: 'goods_num', width: 100, align: 'right', sortable: true },
  { title: '总评论数', dataIndex: 'review_num', width: 100, align: 'right', sortable: true },
  { title: '订单新增数', dataIndex: 'order_increase', slotName: 'order_increase', width: 100, align: 'center', sortable: true },
  { title: '环比新增', dataIndex: 'growth_rate', slotName: 'growth_rate', width: 100, align: 'center', sortable: true }
];

const detailData = ref<any[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

// 监听shopId和visible变化，加载店铺明细数据
watch(
  () => [props.shopId, props.visible],
  ([shopId, visible]) => {
    if (shopId && visible) {
      error.value = null;
      fetchShopDetails(String(shopId));
    }
  },
  { immediate: true }
);

// 获取店铺明细数据
const fetchShopDetails = async (shopId: string) => {
  loading.value = true;
  error.value = null;
  
  try {
    // 获取店铺基本数据
    const shopRes: any = await http.get('/items/shop_data', {
      params: {
        filter: {
          mall_id: { _eq: shopId }
        },
        sort: ['-update_time']
      }
    });
    
    if (!shopRes.data || shopRes.data.length === 0) {
      error.value = '未找到店铺数据';
      detailData.value = [];
      return;
    }
    
    // 获取店铺的所有日志数据
    const logRes: any = await http.get('/items/shop_log', {
      params: {
        filter: {
          shop_data: {
            _in: shopRes.data?.map((item: any) => item.id) || []
          }
        },
        sort: ['-log_time']
      }
    });
    
    // 合并数据
    const result = shopRes.data?.map((shop: any) => {
      const shopLog = logRes.data?.find((log: any) => log.shop_data === shop.id) || {};
      return {
        ...shop,
        order_increase: shopLog.order_increase || 0,
        growth_rate: shopLog.growth_rate || 0
      };
    }) || [];
    
    // 按更新时间排序
    detailData.value = result.sort((a: any, b: any) => 
      new Date(b.update_time).getTime() - new Date(a.update_time).getTime()
    );
    
    if (detailData.value.length === 0) {
      error.value = '店铺数据为空';
    }
  } catch (err: any) {
    console.error('获取店铺明细失败:', err);
    error.value = typeof err.message === 'string' ? err.message : '获取店铺明细失败';
    Message.error('获取店铺明细失败');
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-';
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化数字
const formatNumber = (num: number) => {
  if (num === undefined || num === null) return '-';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num.toString();
};

// 格式化百分比
const formatPercent = (val: number) => {
  if (val === undefined || val === null) return '-';
  return (val * 100).toFixed(2) + '%';
};

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
  // 清空数据
  detailData.value = [];
};
</script>

<style scoped>
.shop-detail-container {
  max-height: 500px;
  overflow-y: auto;
  position: relative;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

:deep(.arco-table-th) {
  white-space: nowrap;
  text-align: center !important;
  font-weight: bold !important;
  background-color: #f2f3f5 !important;
}

:deep(.arco-table-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 