import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '../components/layout/MainLayout.vue'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/index.vue')
  },
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: 'shop',
        name: 'ShopList',
        component: () => import('../views/shop/index.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'product',
        name: 'AllProducts',
        component: () => import('../views/product/index.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'product/:shopId',
        name: 'product',
        component: () => import('../views/product/index.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'shop-detail/:shopId',
        name: 'shopDetail',
        component: () => import('../views/shop/detail.vue'),
        meta: { requiresAuth: true }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫，检查用户是否已登录
router.beforeEach((to, _from, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const token = localStorage.getItem('token')

  if (requiresAuth && !token) {
    next('/login')
  } else {
    next()
  }
})

export default router 