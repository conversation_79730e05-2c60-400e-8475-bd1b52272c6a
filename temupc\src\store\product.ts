import { defineStore } from 'pinia'
import { getProductList, getProductLog, getProductsByShopId } from '../api'
import { Message } from '@arco-design/web-vue'

interface ProductData {
  id: number
  update_time: string
  title: string
  sales_num: number
  comment: number
  image_url: string
  price: number
  shop_data: number | { mall_id?: string }
  shop_mall_id?: string
  goods_id: string
  new_orders?: number
  order_growth_rate?: number
  new_comments?: number
  comment_growth_rate?: number
}

export const useProductStore = defineStore('product', {
  state: () => ({
    productList: [] as ProductData[],
    loading: false,
    total: 0,
    currentShopId: null as number | null,
    lastUpdated: null as Date | null,
    // 按店铺ID缓存商品数据
    productCache: new Map<number, {data: ProductData[], timestamp: Date}>()
  }),
  
  getters: {
    isDataStale: (state) => {
      if (!state.lastUpdated) return true
      
      // 数据超过30分钟视为过期
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)
      return state.lastUpdated < thirtyMinutesAgo
    },
    
    // 检查特定店铺的缓存是否存在且未过期
    isShopCacheValid: (state) => (shopId: number) => {
      const cachedData = state.productCache.get(shopId)
      if (!cachedData) return false
      
      // 数据超过15分钟视为过期
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000)
      return cachedData.timestamp > fifteenMinutesAgo
    }
  },
  
  actions: {
    async getProductList(params: any = {}) {
      this.loading = true
      try {
        // 添加排序，默认按更新时间降序
        if (!params.sort) {
          params.sort = ['-update_time']
        }
        
        const res: any = await getProductList(params)
        
        // 获取商品日志数据
        const logRes: any = await getProductLog({
          sort: ['-log_time']
        })
        
        let productListWithStats = []
        if (res.data) {
          // 处理商品列表数据，合并日志数据计算增长数据
          productListWithStats = res.data.map((product: any) => {
            // 处理店铺ID
            let shop_mall_id = null;
            if (typeof product.shop_data === 'object' && product.shop_data && product.shop_data.mall_id) {
              shop_mall_id = product.shop_data.mall_id;
            }
            
            // 找到该商品的最新日志
            const productLogs = logRes.data?.filter((log: any) => log.product_data === product.id)
              .sort((a: any, b: any) => new Date(b.log_time).getTime() - new Date(a.log_time).getTime()) || []
            
            // 如果有日志数据，计算新增订单、评论和增长率
            if (productLogs.length > 0) {
              return {
                ...product,
                shop_mall_id,
                new_orders: productLogs[0]?.order_increase || 0,
                order_growth_rate: productLogs[0]?.order_growth_rate || 0,
                new_comments: productLogs[0]?.comment_increase || 0,
                comment_growth_rate: productLogs[0]?.comment_growth_rate || 0
              }
            }
            
            return {
              ...product,
              shop_mall_id
            }
          })
        }
        
        this.productList = productListWithStats
        this.total = res.meta?.total_count || 0
        this.lastUpdated = new Date()
        
        // 如果是特定店铺的数据，缓存这些数据
        if (params.filter?.shop_data?._eq) {
          const shopId = params.filter.shop_data._eq
          this.productCache.set(shopId, {
            data: productListWithStats,
            timestamp: new Date()
          })
        }
        
        return true
      } catch (error) {
        console.error('获取商品列表失败', error)
        Message.error('获取商品列表失败，请重试')
        return false
      } finally {
        this.loading = false
      }
    },
    
    async getProductsByShopId(shopId: number) {
      this.currentShopId = shopId
      
      // 检查是否有有效的缓存
      if (this.isShopCacheValid(shopId)) {
        const cachedData = this.productCache.get(shopId)
        if (cachedData) {
          this.productList = cachedData.data
          this.total = cachedData.data.length
          return true
        }
      }
      
      this.loading = true
      try {
        const res: any = await getProductsByShopId(shopId)
        
        // 获取商品日志数据
        const logRes: any = await getProductLog({
          sort: ['-log_time']
        })
        
        let productListWithStats = []
        if (res.data) {
          // 处理商品列表数据，合并日志数据计算增长数据
          productListWithStats = res.data.map((product: any) => {
            // 处理店铺ID
            let shop_mall_id = null;
            if (typeof product.shop_data === 'object' && product.shop_data && product.shop_data.mall_id) {
              shop_mall_id = product.shop_data.mall_id;
            }
            
            // 找到该商品的最新日志
            const productLogs = logRes.data?.filter((log: any) => log.product_data === product.id)
              .sort((a: any, b: any) => new Date(b.log_time).getTime() - new Date(a.log_time).getTime()) || []
            
            // 如果有日志数据，计算新增订单、评论和增长率
            if (productLogs.length > 0) {
              return {
                ...product,
                shop_mall_id,
                new_orders: productLogs[0]?.order_increase || 0,
                order_growth_rate: productLogs[0]?.order_growth_rate || 0,
                new_comments: productLogs[0]?.comment_increase || 0,
                comment_growth_rate: productLogs[0]?.comment_growth_rate || 0
              }
            }
            
            return {
              ...product,
              shop_mall_id
            }
          })
        }
        
        this.productList = productListWithStats
        this.total = res.meta?.total_count || 0
        this.lastUpdated = new Date()
        
        // 缓存该店铺的数据
        this.productCache.set(shopId, {
          data: productListWithStats,
          timestamp: new Date()
        })
        
        return true
      } catch (error) {
        console.error('获取商品列表失败', error)
        Message.error('获取商品列表失败，请重试')
        return false
      } finally {
        this.loading = false
      }
    },
    
    // 清除所有缓存
    clearCache() {
      this.productCache.clear()
      this.lastUpdated = null
    }
  }
}) 