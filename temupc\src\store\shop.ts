import { defineStore } from 'pinia'
import { getShopList, getShopLog } from '../api'
import { Message } from '@arco-design/web-vue'

interface ShopData {
  id: number
  update_time: string
  mall_name: string
  review_num: number
  mall_logo: string
  goods_num: number
  goods_sales_num: number
  mall_id: string
  new_orders?: number
  growth_rate?: number
}

export const useShopStore = defineStore('shop', {
  state: () => ({
    shopList: [] as ShopData[],
    loading: false,
    total: 0,
    lastUpdated: null as Date | null
  }),
  
  getters: {
    isDataStale: (state) => {
      if (!state.lastUpdated) return true
      
      // 数据超过30分钟视为过期
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)
      return state.lastUpdated < thirtyMinutesAgo
    }
  },
  
  actions: {
    async getShopList(params: any = {}) {
      this.loading = true
      try {
        // 添加排序，默认按更新时间降序
        if (!params.sort) {
          params.sort = ['-update_time']
        }
        
        const res: any = await getShopList(params)
        
        // 获取店铺日志数据
        const logRes: any = await getShopLog({
          sort: ['-log_time']
        })
        
        let shopListWithStats = []
        if (res.data) {
          // 处理店铺列表数据，合并日志数据计算增长数据
          shopListWithStats = res.data.map((shop: ShopData) => {
            // 找到该店铺的日志
            const shopLogs = logRes.data?.filter((log: any) => log.shop_data === shop.id)
              .sort((a: any, b: any) => new Date(b.log_time).getTime() - new Date(a.log_time).getTime()) || []
            
            // 如果有日志数据，计算新增订单和增长率
            if (shopLogs.length > 0) {
              return {
                ...shop,
                new_orders: shopLogs[0]?.order_increase || 0,
                growth_rate: shopLogs[0]?.growth_rate || 0
              }
            }
            
            return shop
          })
        }
        
        this.shopList = shopListWithStats
        this.total = res.meta?.total_count || 0
        this.lastUpdated = new Date()
        
        return true
      } catch (error) {
        console.error('获取店铺列表失败', error)
        Message.error('获取店铺列表失败，请重试')
        return false
      } finally {
        this.loading = false
      }
    }
  }
}) 