import { defineStore } from 'pinia'
import { login } from '../api'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}')
  }),
  
  actions: {
    async login(email: string, password: string) {
      try {
        const res: any = await login({ email, password })
        if (res.data?.access_token) {
          this.token = res.data.access_token
          this.userInfo = res.data
          
          // 存储到localStorage
          localStorage.setItem('token', res.data.access_token)
          localStorage.setItem('userInfo', JSON.stringify(res.data))
          return true
        }
        return false
      } catch (error) {
        console.error('登录失败', error)
        return false
      }
    },
    
    logout() {
      this.token = ''
      this.userInfo = {}
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }
  }
}) 