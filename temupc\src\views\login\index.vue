<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-content">
        <div class="login-header">
          <div class="logo">
            <icon-shop-fill size="32" />
            <span class="logo-text">店铺分析系统</span>
          </div>
          <div class="desc">欢迎使用Temu商家管理分析平台</div>
        </div>
        <a-card class="login-card" :bordered="false">
          <a-form :model="form" @submit="handleSubmit" layout="vertical" size="large">
            <a-form-item field="email" :rules="[{ required: true, message: '请输入用户名' }]">
              <template #label>
                <span class="form-label">
                  <icon-user />
                  <span class="label-text">用户名</span>
                </span>
              </template>
              <a-input v-model="form.email" placeholder="请输入用户名" allow-clear />
            </a-form-item>
            <a-form-item field="password" :rules="[{ required: true, message: '请输入密码' }]">
              <template #label>
                <span class="form-label">
                  <icon-lock />
                  <span class="label-text">密码</span>
                </span>
              </template>
              <a-input-password v-model="form.password" placeholder="请输入密码" allow-clear />
            </a-form-item>
            <div class="login-option">
              <a-checkbox>记住账号</a-checkbox>
              <a-link>忘记密码？</a-link>
            </div>
            <a-form-item>
              <a-button type="primary" html-type="submit" long :loading="loading" size="large">
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </div>
    </div>
    <div class="login-footer">
      <div class="footer-links">
        <a-link>隐私政策</a-link>
        <a-divider direction="vertical" />
        <a-link>用户协议</a-link>
        <a-divider direction="vertical" />
        <a-link>关于我们</a-link>
      </div>
      <div class="copyright">© {{ new Date().getFullYear() }} Temu店铺分析系统 - 版权所有</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { useUserStore } from '../../store/user'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)

const form = reactive({
  email: '',
  password: ''
})

const handleSubmit = async () => {
  loading.value = true
  try {
    const success = await userStore.login(form.email, form.password)
    if (success) {
      Message.success('登录成功')
      router.push('/shop')
    } else {
      Message.error('登录失败，请检查用户名和密码')
    }
  } catch (error) {
    console.error('登录出错', error)
    Message.error('登录出错，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f7f9;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(91, 143, 249, 0.1) 0%, rgba(123, 213, 245, 0.1) 100%);
  z-index: 0;
}

.login-box {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex: 1;
}

.login-content {
  max-width: 460px;
  width: 100%;
  padding: 0 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  color: var(--color-primary);
}

.logo-text {
  font-size: 28px;
  font-weight: 600;
  margin-left: 12px;
  background: linear-gradient(to right, #165DFF, #41B883);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.desc {
  font-size: 16px;
  color: var(--color-text-3);
  margin-bottom: 30px;
}

.login-card {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 
              0 9px 28px 0 rgba(0, 0, 0, 0.05), 
              0 12px 48px 16px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.login-card:hover {
  box-shadow: 0 8px 24px -8px rgba(0, 0, 0, 0.1), 
              0 12px 32px 0 rgba(0, 0, 0, 0.08), 
              0 16px 64px 16px rgba(0, 0, 0, 0.05);
}

.form-label {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.label-text {
  margin-left: 8px;
}

.login-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.login-footer {
  position: relative;
  z-index: 1;
  width: 100%;
  text-align: center;
  padding: 20px 0;
}

.footer-links {
  margin-bottom: 8px;
}

.copyright {
  font-size: 14px;
  color: var(--color-text-3);
}
</style> 