<template>
  <div class="product-container">
    <a-card :title="shopId ? `商品列表 (店铺ID: ${shopId})` : '所有商品列表'">
      <template #extra>
        <a-button type="primary" @click="fetchProductList" :loading="isRefreshing">
          {{ isRefreshing ? '正在刷新' : '刷新数据' }}
        </a-button>
      </template>
      
      <div class="card-content">
        <!-- 搜索表单 -->
        <div class="search-form-container">
          <a-form :model="searchForm" layout="inline" @submit="handleSearch">
            <a-form-item field="goods_id" label="商品ID">
              <a-input v-model="searchForm.goods_id" placeholder="请输入商品ID" />
            </a-form-item>
            <a-form-item field="shop_mall_id" label="店铺ID">
              <a-input v-model="searchForm.shop_mall_id" placeholder="请输入店铺ID" />
            </a-form-item>
            <a-form-item field="date" label="采集时间">
              <a-range-picker v-model="searchForm.date" />
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit">
                  搜索
                </a-button>
                <a-button @click="resetSearch">
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
        
        <!-- 表格区域 -->
        <div class="table-container">
          <div v-if="!productStore.productList.length && !productStore.loading" class="empty-data">
            <a-empty description="暂无商品数据" />
          </div>
          
          <a-table 
            v-else
            :columns="columns" 
            :data="productStore.productList" 
            :loading="productStore.loading"
            :pagination="{
              total: productStore.total,
              current: currentPage,
              pageSize: pageSize,
              onChange: handlePageChange
            }"
            stripe
            :row-key="(record: ProductData) => record.id"
            :scroll="{ x: true, y: true }"
            border="cell"
            size="medium"
            class="custom-table"
          >
            <template #update_time="{ record }">
              {{ formatDate(record.update_time) }}
            </template>
            
            <template #shop_mall_id="{ record }">
              {{ record.shop_mall_id || '-' }}
            </template>
            
            <template #image_url="{ record }">
              <div class="image-container">
                <a-image-preview-group infinite>
                  <a-image 
                    v-if="record.image_url" 
                    :src="record.image_url" 
                    :preview-src="record.image_url"
                    width="60" 
                    height="60"
                    alt="商品图片"
                    fit="cover"
                    :preview-visible="false"
                    class="product-image"
                  />
                  <a-avatar v-else :size="60">暂无图片</a-avatar>
                </a-image-preview-group>
              </div>
            </template>
            
            <template #title="{ record }">
              <div class="product-title-cell">
                <a-tooltip :content="record.title" position="top">
                  <div class="product-title">{{ record.title }}</div>
                </a-tooltip>
              </div>
            </template>
            
            <template #price="{ record }">
              <div class="price-tag">${{ formatPrice(record.price) }}</div>
            </template>
            
            <template #new_orders="{ record }">
              <a-tag v-if="record.new_orders > 0" color="green">+{{ record.new_orders }}</a-tag>
              <a-tag v-else-if="record.new_orders < 0" color="red">{{ record.new_orders }}</a-tag>
              <span v-else>-</span>
            </template>
            
            <template #order_growth_rate="{ record }">
              <a-tag v-if="record.order_growth_rate > 0" color="green">+{{ formatPercent(record.order_growth_rate) }}</a-tag>
              <a-tag v-else-if="record.order_growth_rate < 0" color="red">{{ formatPercent(record.order_growth_rate) }}</a-tag>
              <span v-else>-</span>
            </template>
            
            <template #new_comments="{ record }">
              <a-tag v-if="record.new_comments > 0" color="green">+{{ record.new_comments }}</a-tag>
              <a-tag v-else-if="record.new_comments < 0" color="red">{{ record.new_comments }}</a-tag>
              <span v-else>-</span>
            </template>
            
            <template #comment_growth_rate="{ record }">
              <a-tag v-if="record.comment_growth_rate > 0" color="green">+{{ formatPercent(record.comment_growth_rate) }}</a-tag>
              <a-tag v-else-if="record.comment_growth_rate < 0" color="red">{{ formatPercent(record.comment_growth_rate) }}</a-tag>
              <span v-else>-</span>
            </template>
            
            <template #operations="{ record }">
              <a-button type="primary" size="small" status="success" @click="showProductDetail(record.goods_id)">
                <template #icon><icon-search /></template>
                销量明细
              </a-button>
            </template>
          </a-table>
        </div>
      </div>
    </a-card>

    <!-- 商品明细模态窗 -->
    <product-detail-modal
      :visible="detailModalVisible"
      @update:visible="detailModalVisible = $event"
      :product-id="selectedProductId"
      @close="hideDetailModal"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useProductStore } from '../../store/product'
import { Message } from '@arco-design/web-vue'
import { IconSearch } from '@arco-design/web-vue/es/icon'
import ProductDetailModal from '../../components/product/ProductDetailModal.vue'

const route = useRoute()
const productStore = useProductStore()
const isRefreshing = ref(false)
const detailModalVisible = ref(false)
const selectedProductId = ref('')

const shopId = ref<number>(Number(route.params.shopId) || 0)

// 监听路由参数变化
watch(() => route.params.shopId, (newShopId) => {
  console.log('路由参数shopId变化:', newShopId)
  if (newShopId) {
    shopId.value = Number(newShopId)
    fetchProductList()
  }
})

const columns = [
  { title: '采集时间', dataIndex: 'update_time', slotName: 'update_time', width: 120, align: 'center' },
  { title: '店铺ID', dataIndex: 'shop_mall_id', slotName: 'shop_mall_id', width: 110, align: 'center' },
  { title: '商品ID', dataIndex: 'goods_id', width: 110, align: 'center' },
  { title: '商品图片', dataIndex: 'image_url', slotName: 'image_url', width: 80, align: 'center' },
  { title: '商品标题', dataIndex: 'title', slotName: 'title', width: 180, ellipsis: true },
  { title: '销售量', dataIndex: 'sales_num', width: 80, align: 'right' },
  { title: '销售价格', dataIndex: 'price', slotName: 'price', width: 80, align: 'right' },
  { title: '评论数', dataIndex: 'comment', width: 80, align: 'right' },
  { title: '新增订单数', dataIndex: 'new_orders', slotName: 'new_orders', width: 90, align: 'center' },
  { title: '环比订单增长比例', dataIndex: 'order_growth_rate', slotName: 'order_growth_rate', width: 130, align: 'center' },
  { title: '新增评论数', dataIndex: 'new_comments', slotName: 'new_comments', width: 90, align: 'center' },
  { title: '评论环比增长比例', dataIndex: 'comment_growth_rate', slotName: 'comment_growth_rate', width: 130, align: 'center' },
  { title: '操作', slotName: 'operations', width: 120, fixed: 'right', align: 'center' }
]

const currentPage = ref(1)
const pageSize = ref(50)
const searchForm = reactive({
  goods_id: '',
  shop_mall_id: '',
  date: [] as Date[]
})

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化价格
const formatPrice = (price: number) => {
  if (price === undefined || price === null) return '-'
  return price.toFixed(2)
}

// 格式化百分比
const formatPercent = (val: number) => {
  if (val === undefined || val === null) return '-'
  return (val * 100).toFixed(2) + '%'
}

// 获取商品列表数据
const fetchProductList = async () => {
  console.log('开始获取商品列表，店铺ID:', shopId.value || '全部商品')
  isRefreshing.value = true
  
  const params: any = {
    page: currentPage.value,
    limit: pageSize.value,
    filter: {}
  }
  
  // 只有在有店铺ID的情况下才添加店铺过滤条件
  if (shopId.value) {
    params.filter.shop_data = {
      _eq: shopId.value
    }
  }
  
  // 添加搜索条件
  if (searchForm.goods_id) {
    params.filter = {
      ...params.filter,
      goods_id: {
        _eq: searchForm.goods_id
      }
    }
  }
  
  if (searchForm.shop_mall_id) {
    params.filter = {
      ...params.filter,
      shop_data: {
        mall_id: {
          _eq: searchForm.shop_mall_id
        }
      }
    }
  }
  
  if (searchForm.date && searchForm.date.length === 2) {
    params.filter = {
      ...params.filter,
      update_time: {
        _between: [
          searchForm.date[0]?.toISOString(),
          searchForm.date[1]?.toISOString()
        ]
      }
    }
  }
  
  try {
    const success = await productStore.getProductList(params)
    console.log('商品列表获取成功，数量:', productStore.productList.length)
    
    if (success) {
      Message.success('数据刷新成功')
    }
    
    if (productStore.productList.length === 0) {
      Message.info('该店铺暂无商品数据')
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    Message.error('获取商品列表失败，请重试')
  } finally {
    isRefreshing.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchProductList()
}

// 重置搜索条件
const resetSearch = () => {
  searchForm.goods_id = ''
  searchForm.shop_mall_id = ''
  searchForm.date = []
  currentPage.value = 1
  fetchProductList()
}

// 处理分页
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchProductList()
}

// 显示商品明细弹窗
const showProductDetail = (goodsId: string) => {
  selectedProductId.value = goodsId
  detailModalVisible.value = true
}

// 隐藏商品明细弹窗
const hideDetailModal = () => {
  detailModalVisible.value = false
}

onMounted(() => {
  console.log('商品列表页面加载，路由参数:', route.params)
  
  // 显示加载中提示
  Message.loading({
    content: '正在加载商品数据...',
    duration: 0
  })
  
  if (shopId.value) {
    productStore.currentShopId = shopId.value
  } else {
    productStore.currentShopId = 0 // 0表示查询所有商品
  }
  
  fetchProductList().finally(() => {
    // 关闭加载提示
    Message.clear()
  })
})
</script>

<style scoped>
.product-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.search-form-container {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.table-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.product-image {
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.product-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.product-title-cell {
  max-width: 180px;
  overflow: hidden;
}

.product-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
}

.price-tag {
  font-weight: 600;
  color: #f53f3f;
}

:deep(.arco-table-container) {
  height: 100%;
}

:deep(.arco-table-content) {
  height: 100%;
}

:deep(.arco-table-header) {
  background-color: #f2f3f5;
}

:deep(.arco-table-th) {
  font-weight: 600;
  white-space: nowrap;
  padding: 8px 12px;
}

:deep(.arco-table-td) {
  padding: 8px 12px;
}

:deep(.arco-table-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 操作列样式优化 */
:deep(.arco-table-td[class*="operations"]) {
  padding: 5px 8px;
  background-color: rgba(var(--primary-1), 0.1);
}

:deep(.arco-btn) {
  height: 28px;
  padding: 0 8px;
  font-size: 13px;
}

/* 表格条纹样式 */
:deep(.arco-table-tr:nth-child(even)) {
  background-color: rgba(var(--primary-1), 0.02);
}

/* 优化图片预览 */
:deep(.arco-image-preview) {
  z-index: 1100 !important;
}

:deep(.arco-image-preview-operations) {
  background-color: rgba(0, 0, 0, 0.7) !important;
}
</style> 