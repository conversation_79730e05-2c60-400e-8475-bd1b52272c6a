<template>
  <div class="shop-detail-container">
    <a-card :title="`店铺详情 (店铺ID: ${currentMallId})`">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="fetchShopHistoryData" :loading="isRefreshing">
            {{ isRefreshing ? '正在刷新' : '刷新数据' }}
          </a-button>
        </a-space>
      </template>
      
      <a-form :model="searchForm" layout="inline" @submit="handleSearch">
        <a-form-item field="mall_id" label="店铺ID">
          <a-input v-model="searchForm.mall_id" placeholder="请输入店铺ID" />
        </a-form-item>
        <a-form-item field="date" label="更新时间">
          <a-range-picker v-model="searchForm.date" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">
            搜索
          </a-button>
          <a-button style="margin-left: 10px" @click="resetSearch">
            重置
          </a-button>
        </a-form-item>
      </a-form>
      
      <div v-if="loading && !shopHistoryData.length" class="loading-container">
        <a-spin tip="加载中..."></a-spin>
      </div>
      
      <div v-else-if="!shopHistoryData.length" class="empty-container">
        <a-result status="warning" subtitle="未找到店铺数据">
          <template #extra>
            <a-button type="primary" @click="fetchShopHistoryData">
              重试
            </a-button>
          </template>
        </a-result>
      </div>
      
      <a-table
        v-else
        :columns="columns"
        :data="shopHistoryData"
        :loading="loading"
        :pagination="{
          total: totalShopHistory,
          current: currentPage,
          pageSize: pageSize,
          onChange: handlePageChange
        }"
        stripe
        :row-key="(record: any) => record.id"
        :scroll="{ x: '100%' }"
        border="cell"
        size="medium"
        class="custom-table"
      >
        <template #update_time="{ record }">
          {{ formatDate(record.update_time) }}
        </template>
        
        <template #mall_logo="{ record }">
          <a-avatar v-if="record.mall_logo" :image-url="record.mall_logo" :size="40" />
          <a-avatar v-else :size="40">{{ record.mall_name?.substring(0, 1) || 'N/A' }}</a-avatar>
        </template>
        
        <template #goods_sales_num="{ record }">
          {{ formatNumber(record.goods_sales_num) }}
        </template>
        
        <template #new_orders="{ record }">
          <a-tag v-if="record.new_orders > 0" color="green">+{{ record.new_orders }}</a-tag>
          <a-tag v-else-if="record.new_orders < 0" color="red">{{ record.new_orders }}</a-tag>
          <span v-else>-</span>
        </template>
        
        <template #growth_rate="{ record }">
          <a-tag v-if="record.growth_rate > 0" color="green">+{{ formatPercent(record.growth_rate) }}</a-tag>
          <a-tag v-else-if="record.growth_rate < 0" color="red">{{ formatPercent(record.growth_rate) }}</a-tag>
          <span v-else>-</span>
        </template>
        
        <template #operations="{ record }">
          <a-button type="primary" size="small" @click="showShopDetail(record.mall_id)">
            店铺明细
          </a-button>
        </template>
      </a-table>
    </a-card>
    
    <!-- 店铺明细弹窗 -->
    <shop-detail-modal
      :visible="detailModalVisible"
      @update:visible="detailModalVisible = $event"
      :shop-id="currentDetailMallId"
      @close="hideDetailModal"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { useShopStore } from '../../store/shop'
import ShopDetailModal from '../../components/shop/ShopDetailModal.vue'
import http from '../../api/http'

const route = useRoute()
const shopStore = useShopStore()

const shopId = computed(() => Number(route.params.shopId) || 0)
const loading = ref(false)
const isRefreshing = ref(false)
const shopData = ref<any>(null)
const shopHistoryData = ref<any[]>([])
const totalShopHistory = ref(0)
const currentPage = ref(1)
const pageSize = ref(50)
const detailModalVisible = ref(false)
const currentMallId = ref('')
const currentDetailMallId = ref('')

const searchForm = reactive({
  mall_id: '',
  date: [] as Date[]
})

const columns = [
  { title: '更新时间', dataIndex: 'update_time', slotName: 'update_time', width: 150, align: 'center' },
  { title: '店铺ID', dataIndex: 'mall_id', width: 120, align: 'center' },
  { title: '店铺LOGO', dataIndex: 'mall_logo', slotName: 'mall_logo', width: 100, align: 'center' },
  { title: '店铺名称', dataIndex: 'mall_name', width: 160, ellipsis: true },
  { title: '店铺销量', dataIndex: 'goods_sales_num', slotName: 'goods_sales_num', width: 100, align: 'right' },
  { title: '商品数量', dataIndex: 'goods_num', width: 100, align: 'right' },
  { title: '总评论数', dataIndex: 'review_num', width: 100, align: 'right' },
  { title: '订单新增数', dataIndex: 'new_orders', slotName: 'new_orders', width: 100, align: 'center' },
  { title: '环比新增', dataIndex: 'growth_rate', slotName: 'growth_rate', width: 100, align: 'center' },
  { title: '操作', slotName: 'operations', width: 100, fixed: 'right', align: 'center' }
]

// 获取店铺详情
const fetchShopDetail = async () => {
  if (!shopId.value) {
    Message.error('未找到店铺ID')
    return
  }
  
  loading.value = true
  
  try {
    // 先获取店铺列表数据
    const params = {
      filter: {
        id: { _eq: shopId.value }
      }
    }
    
    const success = await shopStore.getShopList(params)
    
    if (success && shopStore.shopList.length > 0) {
      shopData.value = shopStore.shopList[0]
      currentMallId.value = shopData.value.mall_id
      // 获取店铺id后，加载该店铺的所有历史数据
      await fetchShopHistoryData()
    } else {
      shopData.value = null
      Message.warning('未找到店铺数据')
    }
  } catch (error) {
    console.error('获取店铺详情失败:', error)
    Message.error('获取店铺详情失败')
    shopData.value = null
  } finally {
    loading.value = false
  }
}

// 获取店铺历史数据
const fetchShopHistoryData = async () => {
  isRefreshing.value = true
  
  const params: any = {
    page: currentPage.value,
    limit: pageSize.value,
    sort: ['-update_time']
  }
  
  // 优先使用搜索条件中的店铺ID
  const targetMallId = searchForm.mall_id || currentMallId.value
  
  if (targetMallId) {
    params.filter = {
      ...params.filter,
      mall_id: { _eq: targetMallId }
    }
  }
  
  // 添加日期搜索条件
  if (searchForm.date && searchForm.date.length === 2) {
    params.filter = {
      ...params.filter,
      update_time: {
        _between: [
          searchForm.date[0]?.toISOString(),
          searchForm.date[1]?.toISOString()
        ]
      }
    }
  }
  
  try {
    // 获取店铺历史数据
    const res: any = await http.get('/items/shop_data', { params })
    
    // 获取店铺日志数据
    const logRes: any = await http.get('/items/shop_log', {
      params: {
        sort: ['-log_time']
      }
    })
    
    // 合并数据
    let shopListWithStats = []
    if (res.data) {
      // 处理店铺列表数据，合并日志数据计算增长数据
      shopListWithStats = res.data.map((shop: any) => {
        // 找到该店铺的日志
        const shopLogs = logRes.data?.filter((log: any) => log.shop_data === shop.id)
          .sort((a: any, b: any) => new Date(b.log_time).getTime() - new Date(a.log_time).getTime()) || []
        
        // 如果有日志数据，计算新增订单和增长率
        if (shopLogs.length > 0) {
          return {
            ...shop,
            new_orders: shopLogs[0]?.order_increase || 0,
            growth_rate: shopLogs[0]?.growth_rate || 0
          }
        }
        
        return shop
      })
    }
    
    shopHistoryData.value = shopListWithStats
    totalShopHistory.value = res.meta?.total_count || 0
    
    if (shopHistoryData.value.length === 0) {
      Message.warning('未找到店铺历史数据')
    } else {
      Message.success('数据刷新成功')
    }
  } catch (error) {
    console.error('获取店铺历史数据失败:', error)
    Message.error('获取店铺历史数据失败')
  } finally {
    isRefreshing.value = false
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchShopHistoryData()
}

// 重置搜索条件
const resetSearch = () => {
  searchForm.mall_id = ''
  searchForm.date = []
  currentPage.value = 1
  fetchShopHistoryData()
}

// 处理分页
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchShopHistoryData()
}

// 显示明细弹窗
const showShopDetail = (mallId: string) => {
  if (!mallId) {
    Message.warning('未找到店铺ID，无法查看明细')
    return
  }
  
  currentDetailMallId.value = mallId
  detailModalVisible.value = true
}

// 隐藏明细弹窗
const hideDetailModal = () => {
  detailModalVisible.value = false
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num === undefined || num === null) return '-'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toString()
}

// 格式化百分比
const formatPercent = (val: number) => {
  if (val === undefined || val === null) return '-'
  return (val * 100).toFixed(2) + '%'
}

onMounted(() => {
  fetchShopDetail()
})
</script>

<style scoped>
.shop-detail-container {
  padding: 20px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
}

.shop-info {
  margin-top: 20px;
}

.detail-row {
  display: flex;
  margin-top: 20px;
  gap: 20px;
}

.logo-container,
.stats-container {
  flex: 1;
}

.logo-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.stats-card {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

:deep(.arco-form) {
  margin-bottom: 16px;
  width: 100%;
}

:deep(.arco-descriptions-item-label) {
  background-color: #f5f5f5;
  font-weight: bold;
}

:deep(.arco-statistic) {
  margin-bottom: 20px;
}

/* 表格样式优化 */
.custom-table {
  width: 100%;
  table-layout: fixed;
}

:deep(.arco-table-th) {
  white-space: nowrap;
  text-align: center !important;
  font-weight: bold !important;
  background-color: #f2f3f5 !important;
}

:deep(.arco-table-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 