<template>
  <div class="shop-container">
    <a-card title="店铺列表">
      <template #extra>
        <a-button type="primary" @click="fetchShopList" :loading="isRefreshing">
          {{ isRefreshing ? '正在刷新' : '刷新数据' }}
        </a-button>
      </template>
      
      <div class="card-content">
        <!-- 搜索表单 -->
        <div class="search-form-container">
          <a-form :model="searchForm" layout="inline" @submit="handleSearch">
            <a-form-item field="mall_id" label="店铺ID">
              <a-input v-model="searchForm.mall_id" placeholder="请输入店铺ID" />
            </a-form-item>
            <a-form-item field="date" label="更新时间">
              <a-range-picker v-model="searchForm.date" />
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit">
                  搜索
                </a-button>
                <a-button @click="resetSearch">
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
        
        <!-- 表格区域 -->
        <div class="table-container">
          <a-table 
            :columns="columns" 
            :data="shopStore.shopList" 
            :loading="shopStore.loading"
            :pagination="{
              total: shopStore.total,
              current: currentPage,
              pageSize: pageSize,
              onChange: handlePageChange
            }"
            stripe
            :row-key="(record: ShopData) => record.id"
            :scroll="{ x: true, y: true }"
            border="cell"
            size="medium"
            class="custom-table"
          >
            <template #update_time="{ record }">
              {{ formatDate(record.update_time) }}
            </template>
            
            <template #mall_logo="{ record }">
              <a-avatar v-if="record.mall_logo" :image-url="record.mall_logo" :size="40" />
              <a-avatar v-else :size="40">{{ record.mall_name?.substring(0, 1) || 'N/A' }}</a-avatar>
            </template>
            
            <template #mall_name="{ record }">
              <a-link @click="navigateToProductList(record.id)">{{ record.mall_name || '-' }}</a-link>
            </template>
            
            <template #goods_sales_num="{ record }">
              {{ formatNumber(record.goods_sales_num) }}
            </template>
            
            <template #new_orders="{ record }">
              <a-tag v-if="record.new_orders > 0" color="green">+{{ record.new_orders }}</a-tag>
              <a-tag v-else-if="record.new_orders < 0" color="red">{{ record.new_orders }}</a-tag>
              <span v-else>-</span>
            </template>
            
            <template #growth_rate="{ record }">
              <a-tag v-if="record.growth_rate > 0" color="green">+{{ formatPercent(record.growth_rate) }}</a-tag>
              <a-tag v-else-if="record.growth_rate < 0" color="red">{{ formatPercent(record.growth_rate) }}</a-tag>
              <span v-else>-</span>
            </template>
            
            <template #operations="{ record }">
              <a-space>
                <a-button type="primary" size="small" status="success" @click="navigateToProductList(record.id)">
                  <template #icon><icon-eye /></template>
                  查看商品
                </a-button>
                <a-button type="primary" size="small" status="warning" @click="navigateToShopDetail(record.id, record.mall_id)">
                  <template #icon><icon-info-circle /></template>
                  查看店铺
                </a-button>
              </a-space>
            </template>
          </a-table>
        </div>
      </div>
    </a-card>

    <!-- 店铺明细弹窗 -->
    <shop-detail-modal
      :visible="detailModalVisible"
      @update:visible="detailModalVisible = $event"
      :shop-id="currentShopMallId"
      @close="hideShopDetail"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useShopStore } from '../../store/shop'
import { useProductStore } from '../../store/product'
import { Message } from '@arco-design/web-vue'
import { IconEye, IconInfoCircle } from '@arco-design/web-vue/es/icon'
import ShopDetailModal from '../../components/shop/ShopDetailModal.vue'

const router = useRouter()
const shopStore = useShopStore()
const productStore = useProductStore()
const isRefreshing = ref(false)

// 店铺明细相关
const detailModalVisible = ref(false)
const currentShopMallId = ref('')

const columns = [
  { title: '更新时间', dataIndex: 'update_time', slotName: 'update_time', width: 120, align: 'center' },
  { title: '店铺ID', dataIndex: 'mall_id', width: 110, align: 'center' },
  { title: '店铺LOGO', dataIndex: 'mall_logo', slotName: 'mall_logo', width: 80, align: 'center' },
  { title: '店铺名称', dataIndex: 'mall_name', slotName: 'mall_name', width: 140, ellipsis: true },
  { title: '店铺销量', dataIndex: 'goods_sales_num', slotName: 'goods_sales_num', width: 80, align: 'right' },
  { title: '商品数量', dataIndex: 'goods_num', width: 80, align: 'right' },
  { title: '总评论数', dataIndex: 'review_num', width: 80, align: 'right' },
  { title: '订单新增数', dataIndex: 'new_orders', slotName: 'new_orders', width: 90, align: 'center' },
  { title: '环比新增', dataIndex: 'growth_rate', slotName: 'growth_rate', width: 90, align: 'center' },
  { title: '操作', slotName: 'operations', width: 200, fixed: 'right', align: 'center' }
]

const currentPage = ref(1)
const pageSize = ref(50)
const searchForm = reactive({
  mall_id: '',
  date: [] as Date[]
})

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num === undefined || num === null) return '-'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toString()
}

// 格式化百分比
const formatPercent = (val: number) => {
  if (val === undefined || val === null) return '-'
  return (val * 100).toFixed(2) + '%'
}

// 获取店铺列表数据
const fetchShopList = async () => {
  isRefreshing.value = true
  
  const params: any = {
    page: currentPage.value,
    limit: pageSize.value
  }
  
  // 添加搜索条件
  if (searchForm.mall_id) {
    params.filter = {
      ...params.filter,
      mall_id: {
        _eq: searchForm.mall_id
      }
    }
  }
  
  if (searchForm.date && searchForm.date.length === 2) {
    params.filter = {
      ...params.filter,
      update_time: {
        _between: [
          searchForm.date[0]?.toISOString(),
          searchForm.date[1]?.toISOString()
        ]
      }
    }
  }
  
  try {
    const success = await shopStore.getShopList(params)
    if (success) {
      Message.success('数据刷新成功')
    }
  } catch (error) {
    console.error('获取店铺列表出错:', error)
    Message.error('获取店铺列表失败')
  } finally {
    isRefreshing.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchShopList()
}

// 重置搜索条件
const resetSearch = () => {
  searchForm.mall_id = ''
  searchForm.date = []
  currentPage.value = 1
  fetchShopList()
}

// 处理分页
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchShopList()
}

// 跳转到商品列表
const navigateToProductList = (shopId: number) => {
  console.log('跳转到商品列表，店铺ID:', shopId)
  productStore.currentShopId = shopId
  router.push(`/product/${shopId}`)
}

// 跳转到店铺详情
const navigateToShopDetail = (shopId: number, mallId: string) => {
  console.log('跳转到店铺详情，店铺ID:', shopId, '商城ID:', mallId)
  router.push(`/shop-detail/${shopId}`)
}

// 隐藏店铺明细
const hideShopDetail = () => {
  detailModalVisible.value = false
  currentShopMallId.value = ''
}

onMounted(() => {
  fetchShopList()
})
</script>

<style scoped>
.shop-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.search-form-container {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.table-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

:deep(.arco-table-container) {
  height: 100%;
}

:deep(.arco-table-content) {
  height: 100%;
}

:deep(.arco-table-header) {
  background-color: #f2f3f5;
}

:deep(.arco-table-th) {
  font-weight: 600;
  white-space: nowrap;
  padding: 8px 12px;
}

:deep(.arco-table-td) {
  padding: 10px 12px;
}

:deep(.arco-table-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.arco-table-operation-cell) {
  padding: 4px 8px;
}

/* 操作列样式优化 */
:deep(.arco-table-td[class*="operations"]) {
  padding: 5px 8px;
  background-color: rgba(var(--primary-1), 0.1);
}

:deep(.arco-btn) {
  height: 28px;
  padding: 0 8px;
  font-size: 13px;
}

/* 表格条纹样式 */
:deep(.arco-table-tr:nth-child(even)) {
  background-color: rgba(var(--primary-1), 0.02);
}
</style> 