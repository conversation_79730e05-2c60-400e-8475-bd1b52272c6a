/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 定义Shop类型
interface ShopData {
  id: number;
  update_time: string;
  mall_name: string;
  review_num: number;
  mall_logo: string;
  goods_num: number;
  goods_sales_num: number;
  mall_id: string;
  new_orders?: number;
  growth_rate?: number;
}

// 定义Product类型
interface ProductData {
  id: number;
  update_time: string;
  title: string;
  sales_num: number;
  comment: number;
  image_url: string;
  price: number;
  shop_data: number;
  goods_id: string;
  new_orders?: number;
  order_growth_rate?: number;
  new_comments?: number;
  comment_growth_rate?: number;
}
