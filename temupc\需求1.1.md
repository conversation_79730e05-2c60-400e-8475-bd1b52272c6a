## 1 数据源说明

### 1.1 directus的api获取

需要登录directus才能访问这两个页面

以下是diretcus的toekn信息以及登录地址
    "BASE_URL": "http://172.25.165.28:8055",
    "API_TOKEN": "OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM",

### 1.2 跨域问题

由于到时候店铺管理的入口会与diretcus会配置不同的服务器，因此给你给我写的框架必须解决了跨域名问题，

补充说明：diretcus的跨域我这边是配置好了的，这个代码先本地跑，但是连接的diretcus是服务器上的数据。

### 1.3店铺列表、商品列表数据结构

需用到数据库中的4个表product_data、product_log、shop_data、shop_log，请仔细查看数据库表结构，见dump-dtstemushop-202504101133.sql文件

### 1.4 框架要求

https://arco.design/vue/docs/start 字节的vue

## 2 功能需求列表-店铺分析

统一说明：表格需要有分页功能，默认50页

### 2.1登录

登录后才能进入后台，使用diretcus的用户名密码

### 2.2 店铺列表

说明：展示店铺商品列表的功能，并带筛选功能

筛选时间、店铺ID

| 更新时间 | 店铺ID | 店铺LOGO | 店铺名称 | 店铺销量 | 商品数量 | 总评论数 | 操作 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 2025-03-07 14:00:00 | 6313567470795 |  | Hipe Style Sho | 10万 | 666 | 8928 | 查看商品   查看店铺 |
| 2025-03-01 14:00:00 | 6313567470795 |  | Hipe Style Sho | 10万 | 666 | 8928 | 查看商品   查看店铺 |

### 2.2.1 查看商品明细（店铺ID：6313567470795）

说明：点击查看商品明细获得以下功能入口

筛选时间、商品ID

| 采集时间 | 商品ID | 商品图片 | 商品标题 | 销售量 | 销售价格 | 评论数 | 新增订单数 | 环比订单增长比例 | 新增评论数 | 评论环比增长比例 | 操作 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 2025-03-07 14:00:00 | 601099604375684 |  | 商品标题 | 5 | 7.23 | 0 |  |  |  |  | 销量明细 |

点击销量明细，将弹窗显示该商品ID下面的全部时间段的销量明细包含销售量、销售价格、评论数、新增订单数、环比订单增长比例、新增评论数、评论环比增长比例

### 2.2.2 查看店铺明细（店铺ID：6313567470795）

说明：点击查看店铺明细获得以功能入口

筛选时间、商品ID

| 更新时间 | 店铺ID | 店铺LOGO | 店铺名称 | 店铺销量 | 商品数量 | 总评论数 | 订单新增数 | 环比新增 | 操作 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 2025-03-07 14:00:00 | 6313567470795 |  | Hipe Style Sho | 10万 | 666 | 8928 |  |  | 店铺明细 |

点击店铺明细，将弹窗显示该店铺ID下面的全部时间段的销量明细，包含店铺销量、商品数量、总评论数、订单新增数、环比新增

注：此店铺id不是指 id，是指那个mall_id字段

### 2.3 商品列表

说明：所有店铺下面商品列表的功能，并带筛选功能，可筛选店铺ID、商品ID，此处是以商品维度来展示全部的商品数据的入口

| 采集时间 | 店铺ID | 商品ID | 商品图片 | 商品标题 | 销售量 | 销售价格 | 评论数 | 新增订单数 | 环比订单增长比例 | 新增评论数 | 评论环比增长比例 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 2025-03-07 14:00:00 | 6313567470795 | 601099604375684 |  | 商品标题 | 5 | 7.23 | 0 |  |  |  |  |


项目的根目录在C:\Users\<USER>\Documents\个人文档\dev\temupc\temupc  运行命令的时候不要搞错了