#!/usr/bin/env python3
"""
代理配置测试工具
测试你的代理设置是否能正常访问Temu API
"""

import requests
import json
import time
import os
from datetime import datetime

class ProxyTester:
    def __init__(self, proxy_config=None):
        self.proxy_config = proxy_config
        self.session = requests.Session()
        
        # 如果提供了代理配置，设置代理
        if proxy_config:
            self.session.proxies.update(proxy_config)
        
        # 从你的a.py中复制的headers
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
            "content-type": "application/json;charset=UTF-8",
            "origin": "https://www.temu.com",
            "priority": "u=1, i",
            "referer": "https://www.temu.com/",
            "sec-ch-ua": '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"
        }
        
        # 测试用的payload
        self.test_payload = {
            "mallId": "6313567470795",
            "mainGoodsIds": ["1"],
            "source_page_sn": "10013",
            "mall_id": "6313567470795",
            "main_goods_ids": ["1"],
            "filter_items": "",
            "page_number": 1,
            "page_size": 8,
            "list_id": "r7oe7gyw0vd5xo2z2qja2",
            "scene_code": "mall_rule",
            "page_sn": 10040,
            "page_el_sn": 201265,
            "source": 10018,
            "anti_content": "1"
        }

    def load_cookies_from_file(self):
        """从cookie.json文件加载cookie"""
        try:
            cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
            
            if not os.path.exists(cookie_file_path):
                print(f"⚠️  Cookie文件不存在: {cookie_file_path}")
                return None
            
            with open(cookie_file_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])
            
            print(f"✅ 成功加载cookie，包含 {len(cookies_data)} 个条目")
            return cookie_str
            
        except Exception as e:
            print(f"❌ 加载cookie文件时出错: {str(e)}")
            return None

    def test_basic_connectivity(self):
        """测试基本连通性"""
        print("🔍 测试基本连通性...")
        
        test_urls = [
            "https://www.temu.com",
            "https://api.temu.com",
            "https://www.google.com"
        ]
        
        results = {}
        
        for url in test_urls:
            try:
                print(f"  测试: {url}")
                response = self.session.get(url, timeout=10, allow_redirects=True)
                results[url] = {
                    'status': 'success',
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds()
                }
                print(f"    ✅ 成功 (状态码: {response.status_code}, 响应时间: {response.elapsed.total_seconds():.2f}s)")
            except Exception as e:
                results[url] = {
                    'status': 'failed',
                    'error': str(e)
                }
                print(f"    ❌ 失败: {str(e)}")
        
        return results

    def test_temu_api(self, use_cookies=True):
        """测试Temu API端点"""
        print("🎯 测试Temu API端点...")
        
        api_url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
        
        # 准备headers
        test_headers = self.headers.copy()
        
        if use_cookies:
            cookies = self.load_cookies_from_file()
            if cookies:
                test_headers["Cookie"] = cookies
            else:
                print("⚠️  无法加载cookie，将在没有cookie的情况下测试")
        
        try:
            print(f"  请求URL: {api_url}")
            print(f"  使用代理: {'是' if self.proxy_config else '否'}")
            print(f"  使用Cookie: {'是' if use_cookies and 'Cookie' in test_headers else '否'}")
            
            start_time = time.time()
            response = self.session.post(api_url, headers=test_headers, json=self.test_payload, timeout=15)
            end_time = time.time()
            
            result = {
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'content_length': len(response.content),
                'headers': dict(response.headers)
            }
            
            print(f"  ✅ 请求完成")
            print(f"    状态码: {response.status_code}")
            print(f"    响应时间: {result['response_time']:.2f}s")
            print(f"    响应大小: {result['content_length']} bytes")
            
            # 分析响应内容
            try:
                response_json = response.json()
                result['json_response'] = response_json
                
                if response.status_code == 200:
                    print(f"    🎉 API调用成功!")
                    if 'result' in response_json:
                        print(f"    📊 响应包含result数据")
                    else:
                        print(f"    ⚠️  响应格式可能不正确")
                elif response.status_code == 403:
                    print(f"    🚫 访问被拒绝 (403)")
                    if 'error_code' in response_json:
                        print(f"    错误代码: {response_json.get('error_code')}")
                else:
                    print(f"    ❌ API调用失败")
                
            except json.JSONDecodeError:
                result['raw_response'] = response.text[:500]
                print(f"    ⚠️  响应不是有效的JSON格式")
                print(f"    响应内容: {response.text[:200]}...")
            
            return result
            
        except Exception as e:
            print(f"    ❌ API测试失败: {str(e)}")
            return {
                'status': 'failed',
                'error': str(e)
            }

    def test_with_different_configs(self):
        """使用不同配置进行测试"""
        print("🧪 使用不同配置进行测试...")
        
        configs = [
            {
                'name': '无代理',
                'proxy': None
            },
            {
                'name': 'HTTP代理示例',
                'proxy': {
                    'http': 'http://proxy-server:port',
                    'https': 'http://proxy-server:port'
                }
            },
            {
                'name': 'SOCKS5代理示例',
                'proxy': {
                    'http': 'socks5://proxy-server:port',
                    'https': 'socks5://proxy-server:port'
                }
            }
        ]
        
        results = {}
        
        for config in configs:
            print(f"\n--- 测试配置: {config['name']} ---")
            
            if config['proxy']:
                print("⚠️  这是示例配置，请替换为你的实际代理服务器地址")
                print("跳过代理测试...")
                continue
            
            # 创建新的测试实例
            tester = ProxyTester(config['proxy'])
            
            # 测试基本连通性
            connectivity = tester.test_basic_connectivity()
            
            # 测试API
            api_result = tester.test_temu_api()
            
            results[config['name']] = {
                'connectivity': connectivity,
                'api_result': api_result
            }
        
        return results

    def generate_report(self, results):
        """生成测试报告"""
        report = {
            'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'proxy_config': self.proxy_config,
            'results': results
        }
        
        # 保存报告
        with open('proxy_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 测试报告已保存到: proxy_test_report.json")
        return report

def main():
    print("🚀 Temu代理配置测试工具")
    print("=" * 50)
    
    # 创建测试实例（无代理）
    tester = ProxyTester()
    
    # 测试基本连通性
    connectivity_results = tester.test_basic_connectivity()
    
    print("\n" + "=" * 50)
    
    # 测试API端点
    api_results = tester.test_temu_api()
    
    print("\n" + "=" * 50)
    
    # 生成报告
    all_results = {
        'connectivity': connectivity_results,
        'api_test': api_results
    }
    
    report = tester.generate_report(all_results)
    
    print("\n📝 测试总结:")
    print("-" * 30)
    
    # 分析结果
    if api_results.get('status_code') == 200:
        print("✅ API测试成功！你的配置工作正常。")
    elif api_results.get('status_code') == 403:
        print("🚫 API返回403错误，可能的原因:")
        print("   1. 需要有效的Cookie")
        print("   2. IP被限制，需要使用代理")
        print("   3. 请求头不完整或不正确")
        print("   4. 反爬虫检测")
    else:
        print("❌ API测试失败，请检查网络连接和配置")
    
    print(f"\n💡 建议:")
    print("1. 确保cookie.json文件存在且有效")
    print("2. 如果仍然403错误，配置代理服务器")
    print("3. 使用高质量的住宅代理IP")
    print("4. 添加请求间隔，避免频率过高")

if __name__ == "__main__":
    main()
